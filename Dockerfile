# py3.11 breaks utapi hash generation ...
ARG UV_VERSION

FROM ghcr.io/astral-sh/uv:${UV_VERSION}
ARG UV_INDEX_DEFAULT_USERNAME
ARG UV_INDEX_DEFAULT_PASSWORD

# Silences warnings
ENV UV_LINK_MODE=copy
# Needed for workers commands to execute correctly
ENV UV_PROJECT_ENVIRONMENT=/usr/local
ENV UV_INDEX_DEFAULT_USERNAME=$UV_INDEX_DEFAULT_USERNAME
ENV UV_INDEX_DEFAULT_PASSWORD=$UV_INDEX_DEFAULT_PASSWORD

ARG ENVIRONMENT
ARG SENTRY_PROJECT
ARG VERSION
ARG SENTRY_DSN

ENV ENVIRONMENT=$ENVIRONMENT
ENV SENTRY_PROJECT=$SENTRY_PROJECT
ENV VERSION=$VERSION
ENV SENTRY_DSN=$SENTRY_DSN

RUN --mount=type=bind,source=pyproject.toml,target=pyproject.toml \
    uv sync --no-install-project --compile-bytecode --no-python-downloads --no-dev --python-preference only-system

# Copy the project into the intermediate image
COPY api/ /app/api
# Import config files passed by gitlab CI
COPY .config /app/api/.config

WORKDIR /app

# Launch API by default
CMD ["uvicorn", "api.main:api", "--host", "0.0.0.0", "--port", "80"]
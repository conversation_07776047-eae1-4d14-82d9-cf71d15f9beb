import base64
import hashlib
import hmac
import json
import logging
import random
import urllib.parse
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Tuple

import httpx
import xmltodict
from pydantic import BaseModel, Field, validator

from ...exceptions import (  # noqa: F401
    AccessDenied,
    EntityAlreadyExists,
    NoSuchEntity,
)

logger = logging.getLogger(__name__)


# Pydantic models for parameter validation
class POEClientConfig(BaseModel):
    """Configuration model for POE client initialization"""

    access_key: str = Field(
        ..., min_length=1, description="Access key for authentication"
    )
    secret_key: str = Field(
        ..., min_length=1, description="Secret key for authentication"
    )
    endpoint: str = Field(..., min_length=1, description="POE service endpoint")
    port: str = Field(..., description="POE service port")
    use_https: bool = Field(default=True, description="Whether to use HTTPS")

    @validator("port")
    def validate_port(cls, v):
        try:
            port_int = int(v)
            if not (1 <= port_int <= 65535):
                raise ValueError("Port must be between 1 and 65535")
            return v
        except ValueError as e:
            if "invalid literal" in str(e):
                raise ValueError("Port must be a valid integer")
            raise


class CreateAccountRequest(BaseModel):
    """Request model for creating an account"""

    account_name: str = Field(
        ..., min_length=1, max_length=64, description="Account name"
    )
    account_id: str = Field(..., description="Account ID")
    email: str = Field(..., description="Email address for the account")

    @validator("email")
    def validate_email(cls, v):
        import re

        email_pattern = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
        if not re.match(email_pattern, v):
            raise ValueError("Invalid email format")
        return v

    @validator("account_id")
    def validate_account_id(cls, v):
        v_int = int(v)
        if not (POEClient.MIN_ACCOUNT_ID <= v_int <= POEClient.MAX_ACCOUNT_ID):
            raise ValueError(
                f"Account ID must be between {POEClient.MIN_ACCOUNT_ID} and {POEClient.MAX_ACCOUNT_ID}"
            )
        return v

    @validator("account_name")
    def validate_account_name(cls, v):
        import re

        # Account name should contain only alphanumeric characters, hyphens, and underscores
        if not re.match(r"^[a-zA-Z0-9_-]+$", v):
            raise ValueError(
                "Account name can only contain alphanumeric characters, hyphens, and underscores"
            )
        return v


class ListAccountsRequest(BaseModel):
    """Request model for listing accounts"""

    offset: int = Field(default=0, ge=0, description="Starting offset for pagination")
    limit: int = Field(
        default=100, ge=1, le=1000, description="Maximum number of accounts to return"
    )


class QuotaRequest(BaseModel):
    """Request model for quota operations"""

    account_id: str = Field(..., min_length=1, description="Account ID")
    quota: Optional[int] = Field(None, ge=0, description="Storage quota in bytes")

    @validator("account_id")
    def validate_account_id_str(cls, v):
        try:
            account_id_int = int(v)
            if not (
                POEClient.MIN_ACCOUNT_ID <= account_id_int <= POEClient.MAX_ACCOUNT_ID
            ):
                raise ValueError(
                    f"Account ID must be between {POEClient.MIN_ACCOUNT_ID} and {POEClient.MAX_ACCOUNT_ID}"
                )
        except ValueError as e:
            if "invalid literal" in str(e):
                raise ValueError("Account ID must be a valid integer string")
            raise
        return v


class AccessKeyRequest(BaseModel):
    """Request model for access key operations"""

    account_id: Optional[str] = Field(
        None, description="Account ID for creating access key"
    )
    access_key_id: Optional[str] = Field(None, description="Access key ID for deletion")

    @validator("account_id")
    def validate_account_id_str(cls, v):
        if v is not None:
            try:
                account_id_int = int(v)
                if not (
                    POEClient.MIN_ACCOUNT_ID
                    <= account_id_int
                    <= POEClient.MAX_ACCOUNT_ID
                ):
                    raise ValueError(
                        f"Account ID must be between {POEClient.MIN_ACCOUNT_ID} and {POEClient.MAX_ACCOUNT_ID}"
                    )
            except ValueError as e:
                if "invalid literal" in str(e):
                    raise ValueError("Account ID must be a valid integer string")
                raise
        return v

    @validator("access_key_id")
    def validate_access_key_id(cls, v):
        if v is not None and len(v.strip()) == 0:
            raise ValueError("Access key ID cannot be empty")
        return v

    def validate_for_create(self):
        """Validate that account_id is provided for create operations"""
        if self.account_id is None:
            raise ValueError("account_id is required for creating access keys")

    def validate_for_delete(self):
        """Validate that access_key_id is provided for delete operations"""
        if self.access_key_id is None:
            raise ValueError("access_key_id is required for deleting access keys")


def url_encode(value):
    return urllib.parse.quote(value, safe="")


def get_sign_parameters(params, ak, time_stamp):
    sign_parameters = params.copy()
    sign_parameters.update(
        {
            "AWSAccessKeyId": ak,
            "SignatureMethod": "HmacSHA256",
            "SignatureVersion": "2",
            "Timestamp": time_stamp,
        }
    )
    return sign_parameters


def sign(http_method, host, uri, sign_parameters, secret_key):
    signature_method = sign_parameters["SignatureMethod"]
    data = f"{http_method}\n{host}\n{uri}\n{get_canonicalized_query_string(sign_parameters)}"
    return sign_hmac(data.encode("utf-8"), secret_key, signature_method)


def get_canonicalized_query_string(parameters):
    sorted_params = sorted(parameters.items())
    return "&".join(f"{url_encode(k)}={url_encode(v)}" for k, v in sorted_params)


def sign_hmac(data, key, algorithm):
    if algorithm == "HmacSHA256":
        signature = hmac.new(key.encode("utf-8"), data, hashlib.sha256).digest()
        return base64.b64encode(signature).decode("utf-8")
    raise ValueError("Unsupported algorithm")


class POEClient:
    MAX_ACCOUNT_ID = **********
    MIN_ACCOUNT_ID = 1

    def __init__(
        self,
        access_key: str,
        secret_key: str,
        endpoint: str,
        port: str,
        use_https: bool,
    ):
        # Validate configuration using Pydantic model
        config = POEClientConfig(
            access_key=access_key,
            secret_key=secret_key,
            endpoint=endpoint,
            port=port,
            use_https=use_https,
        )

        self.access_key = config.access_key
        self.secret_key = config.secret_key
        self.endpoint = config.endpoint
        self.port = config.port
        self.use_https = config.use_https

    @staticmethod
    def check_error(r: httpx.Response) -> None:
        if r.status_code == 409:
            raise EntityAlreadyExists("Entity already exists")

        r.raise_for_status()

    async def _action(self, req_map: Dict) -> httpx.Response:
        request_time = datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%S.%fZ")
        request_string = f"https://{self.endpoint}:{self.port}/poe/rest?"

        # Build the request string
        request_string += f"Action={url_encode(req_map['Action'])}"
        if req_map.get("AccountId", None):
            request_string += f"&AccountId={url_encode(req_map['AccountId'])}"
        if req_map.get("AccountName", None):
            request_string += f"&AccountName={url_encode(req_map['AccountName'])}"
        if req_map.get("Email", None):
            request_string += f"&Email={url_encode(req_map['Email'])}"
        if req_map.get("PolicyDocument", None):
            request_string += f"&PolicyDocument={req_map['PolicyDocument']}"
        if req_map.get("AccessKeyId", None):
            request_string += f"&AccessKeyId={url_encode(req_map['AccessKeyId'])}"
        if req_map.get("StartIndex", None):
            request_string += f"&StartIndex={url_encode(req_map['StartIndex'])}"
        if req_map.get("MaxItems", None):
            request_string += f"&MaxItems={url_encode(req_map['MaxItems'])}"

        # Add authentication parameters
        request_string += f"&AWSAccessKeyId={url_encode(self.access_key)}"
        request_string += f"&SignatureMethod={url_encode('HmacSHA256')}"
        request_string += f"&SignatureVersion={url_encode('2')}"
        request_string += f"&Timestamp={url_encode(request_time)}"

        # Generate the signature
        signature = sign(
            "GET",
            f"{self.endpoint}:{self.port}",
            "/poe/rest",
            get_sign_parameters(req_map, self.access_key, request_time),
            self.secret_key,
        )
        request_string += f"&Signature={url_encode(signature)}"

        # Send the request
        async with httpx.AsyncClient(verify=self.use_https) as client:
            r = await client.get(request_string)
            POEClient.check_error(r)

        # Print the response
        logger.debug("Response Message:")
        logger.debug(r.status_code)
        logger.debug(r.headers)
        logger.debug(r.text)

        return r

    @staticmethod
    def validate_account_id(account_id: int) -> None:
        if (
            account_id < POEClient.MIN_ACCOUNT_ID
            or account_id > POEClient.MAX_ACCOUNT_ID
        ):
            raise ValueError(
                f"Account ID must be between {POEClient.MIN_ACCOUNT_ID} and {POEClient.MAX_ACCOUNT_ID}"
            )

    @staticmethod
    def generate_account_id() -> int:
        return random.randint(POEClient.MIN_ACCOUNT_ID, POEClient.MAX_ACCOUNT_ID)

    async def create_account(
        self, account_name: str, email: str, account_id: str
    ) -> Dict[str, Any]:
        # Validate input parameters using Pydantic model
        request = CreateAccountRequest(
            account_name=account_name, email=email, account_id=account_id
        )

        logger.info(f"Creating Huawei POE account: {request.account_name}")

        req_map = {
            "Action": "CreateAccountWithAll",
            "AccountId": request.account_id,
            "AccountName": request.account_name,
            "Email": request.email,
        }
        r = await self._action(req_map)
        dict_data = xmltodict.parse(r.text)
        # dict_data = {'CreateAccountWithAllResponse': {'CreateAccessKeyResult': {'AccessKey': {'AccessKeyId': 'B6A10CCCCB3114F85F1F',
        #                                                                                     'AccountId': '**********',
        #                                                                                     'CreateDate': '2025-07-02T12:51:20.952Z',
        #                                                                                     'SecretAccessKey': 'v2Tik3qmT6ehhbEEpd2XP5uhv6cAAAGXyzEU+Ke0',
        #                                                                                     'Status': 'Active'}},
        #                                             'CreateAccountResult': {'Account': {'AccountId': '**********',
        #                                                                                 'AccountName': 'an006',
        #                                                                                 'Arn': 'arn:aws:iam::**********:root',
        #                                                                                 'CanonicalUserId': '00000197CB3114F8A5D456E01001E4B3',
        #                                                                                 'CreateDate': '2025-07-02T12:51:20.952Z',
        #                                                                                 'Email': '<EMAIL>',
        #                                                                                 'EncryptOption': '0',
        #                                                                                 'KmsType': '0',
        #                                                                                 'Status': 'Active'}},
        #                                             'CreateServiceResult': {'Service': {'CreateDate': '2025-07-02T12:51:20.952Z',
        #                                                                                 'ServiceType': 'BASE',
        #                                                                                 'Status': 'Active'}},
        #                                             'ResponseMetadata': {'RequestId': '5729df6f-3e1c-4195-a2f9-e5917de82333'}}}

        account = (
            dict_data["CreateAccountWithAllResponse"]["CreateAccountResult"]["Account"]
            | dict_data["CreateAccountWithAllResponse"]["CreateAccessKeyResult"][
                "AccessKey"
            ]
        )
        return account

    async def delete_account(self, account_name: str) -> None:
        logger.info(f"Deleting Huawei POE account: {account_name}")

        req_map = {
            "Action": "DeleteAccount",
            "AccountName": account_name,
        }
        r = await self._action(req_map)
        dict_data = xmltodict.parse(r.text)
        logger.debug(dict_data)

    async def get_account(self, account_id: str) -> Dict[str, Any]:
        logger.info(f"Getting Huawei POE account: {account_id}")

        req_map = {
            "Action": "GetAccount",
            "AccountId": account_id,
        }
        r = await self._action(req_map)
        dict_data = xmltodict.parse(r.text)
        vault_account = dict_data["GetAccountResponse"]["GetAccountResult"]["Account"]

        return vault_account

    async def get_account_id(self, account_name: str) -> str:
        logger.info(f"Getting Huawei POE account: {account_name}")

        offset = 0
        limit = 100
        while True:
            accounts = await self.list_accounts(offset=offset, limit=limit)
            account = next(
                (
                    account
                    for account in accounts
                    if account["AccountName"] == account_name
                ),
                None,
            )
            if account:
                return account["AccountId"]

            if len(accounts) < limit:
                raise NoSuchEntity("Missing account")
            offset += limit

    async def list_accounts(
        self, offset: int = 0, limit: int = 100, marker: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        # Validate input parameters using Pydantic model
        request = ListAccountsRequest(offset=offset, limit=limit)

        logger.info("Listing Huawei POE accounts")

        req_map = {
            "Action": "ListAccounts",
            "StartIndex": str(request.offset),
            "MaxItems": str(request.limit),
        }
        r = await self._action(req_map)
        dict_data = xmltodict.parse(r.text)
        vault_accounts = dict_data["ListAccountsResponse"]["ListAccountsResult"][
            "Accounts"
        ]["member"]

        return vault_accounts

    async def create_ak(self, account_id: str) -> Tuple[str, str]:
        # Validate input parameters using Pydantic model
        request = AccessKeyRequest(account_id=account_id, access_key_id=None)
        request.validate_for_create()

        logger.info(f"Creating Huawei POE access key: {request.account_id}")

        req_map = {
            "Action": "CreateAccessKey",
            "AccountId": request.account_id,
        }
        r = await self._action(req_map)
        dict_data = xmltodict.parse(r.text)
        ak = dict_data["CreateAccessKeyResponse"]["CreateAccessKeyResult"][
            "AccessKeyId"
        ]
        sk = dict_data["CreateAccessKeyResponse"]["CreateAccessKeyResult"][
            "SecretAccessKey"
        ]
        return (ak, sk)

    async def delete_ak(self, access_key_id: str) -> None:
        # Validate input parameters using Pydantic model
        request = AccessKeyRequest(access_key_id=access_key_id, account_id=None)
        request.validate_for_delete()

        logger.info(f"Deleting Huawei POE access key: {request.access_key_id}")

        req_map = {
            "Action": "DeleteAccessKey",
            "AccessKeyId": request.access_key_id,
        }
        r = await self._action(req_map)
        # Parse response but don't store unused result for delete operations
        xmltodict.parse(r.text)

    async def get_quota_bytes(self, account_id: str) -> int:
        # Validate input parameters using Pydantic model
        request = QuotaRequest(account_id=account_id, quota=None)

        logger.info(f"Getting Huawei POE quota: {request.account_id}")

        req_map = {
            "Action": "GetStoragePolicy",
            "AccountId": request.account_id,
        }
        r = await self._action(req_map)
        dict_data = xmltodict.parse(r.text)

        # Step 1: Extract the PolicyDocument string
        policy_document_str = dict_data["GetStoragePolicyResponse"][
            "GetStoragePolicyResult"
        ]["PolicyDocument"]

        # Step 2: Convert the PolicyDocument string to a dictionary
        policy_document = json.loads(policy_document_str)

        # Step 3: Extract the StorageQuota value
        quota_bytes = policy_document["Quota"]["StorageQuota"]
        return int(quota_bytes)

    async def set_quota_bytes(self, account_id: str, quota_bytes: int) -> None:
        # Validate input parameters using Pydantic model
        request = QuotaRequest(account_id=account_id, quota=quota_bytes)

        logger.info(f"Setting Huawei POE quota: {request.account_id} - {request.quota}")

        req_map = {
            "Action": "PutStoragePolicy",
            "AccountId": request.account_id,
            "PolicyDocument": f'{{"Quota":{{"StorageQuota":"{str(request.quota)}"}}}}',
        }
        r = await self._action(req_map)
        # Parse response but don't store unused result for set operations
        xmltodict.parse(r.text)

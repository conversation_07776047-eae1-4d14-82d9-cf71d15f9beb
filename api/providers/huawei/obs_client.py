"""
Huawei Cloud Object Storage Service (OBS) client implementation.

This module provides a wrapper around Huawei OBS APIs for S3-compatible operations.
"""

import logging
from enum import Enum, auto
from typing import Any, Dict, List, Optional, Tuple
from urllib.parse import urljoin

import httpx
import humanfriendly
from pydantic import BaseModel

from ...exceptions import (  # noqa: F401
    AccessDenied,
    EntityAlreadyExists,
    NoSuchEntity,
)
from ...models import AccountInstanceResponse

logger = logging.getLogger(__name__)


class Instance(Enum):
    ACCOUNT = auto()
    USER = auto()
    BUCKET = auto()


class OBSAccountMetricsResponse(BaseModel):
    BucketCount: int
    GlobalBucketCount: int
    GlobalObjectCount: int
    GlobalSpaceSize: int
    ObjectCount: int
    Quota: int
    SpaceSize: int


class OBSClient:
    """Huawei Cloud Object Storage Service client"""

    async def __init__(
        self,
        host: str,
        port: str,
        use_https: bool,
        username: str,
        password: str,
        esn: str,
    ):
        """
        Initialize OBS client.

        Args:
            host: Huawei OBS host
            port: Huawei OBS port
            use_https: Whether to use HTTPS
            username: Username for authentication
            password: Password for authentication
            esn: ESN of the platform
        """
        self.host = host
        self.port = port
        self.use_https = use_https
        self.username = username
        self.password = password
        self.base_url = (
            f"{'https' if self.use_https else 'http'}://{self.host}:{self.port}/api/v2/"
        )
        self.esn = esn
        self.x_auth_token, self.x_csrf_token = self._generate_tokens()
        self.headers = {
            "X-Auth-Token": self.x_auth_token,
            "X-CSRF-Token": self.x_csrf_token,
            "Content-Type": "application/json",
        }

    @staticmethod
    def check_error(r: httpx.Response) -> None:
        if r.status_code == 409:
            raise EntityAlreadyExists("Entity already exists")

        r.raise_for_status()
        if r.json()["result"]["code"] != 0:
            logger.error(
                f"Failed to execute request: {r.json()['result']['description']}"
            )
            raise Exception(
                f"Failed to execute request: {r.json()['result']['description']}"
            )

    def _generate_tokens(self) -> Tuple[str, str]:
        """
        Generate authentication tokens for OBS operations.

        Returns:
            Authentication token
        """
        logger.info("Generating Huawei OBS token")

        payload = {"user_name": self.username, "password": self.password}
        headers = {"Content-Type": "application/json"}

        url_session = urljoin(self.base_url, "aa/sessions")
        try:
            async with httpx.AsyncClient(verify=False) as client:
                r = client.post(url_session, headers=headers, json=payload)
                OBSClient.check_error(r)
                response_data = r.json()
                x_auth_token = response_data["data"]["x_auth_token"]
                x_csrf_token = response_data["data"]["x_csrf_token"]

                return x_auth_token, x_csrf_token
        except Exception as e:
            logger.error(f"Failed to generate Huawei OBS token: {str(e)}")
            raise e

    async def create_account(self, account_name: str) -> AccountInstanceResponse:
        """
        Create a new account.

        Args:
            account_name: Name of the account

        Returns:
            Created account information
        """
        logger.info(f"Creating Huawei OBS account: {account_name}")

        account_url = urljoin(self.base_url, "account/accounts")
        payload = {"name": account_name}
        try:
            async with httpx.AsyncClient(verify=False) as client:
                r = await client.post(account_url, json=payload, headers=self.headers)
                OBSClient.check_error(r)
        except Exception as e:
            logger.error(f"Failed to create Huawei OBS account: {str(e)}")
            raise e

        if r.json()["data"] == {}:
            logger.error(f"{r.json()['result']['description']}")
            raise Exception(f"Failed to create Huawei OBS account: {account_name}")

        return r.json()["data"]

    async def get_account(self, account_name: str) -> Optional[Dict]:
        account_url = urljoin(self.base_url, "account/accounts")
        payload = {"name": account_name}
        try:
            async with httpx.AsyncClient(verify=False) as client:
                r = await client.get(account_url, params=payload, headers=self.headers)
                OBSClient.check_error(r)
        except Exception as e:
            logger.error(f"Failed to get Huawei OBS account: {str(e)}")
            raise e

        if r.json()["data"] == {}:
            logger.error(f"{r.json()['result']['description']}")
            return None

        return r.json()["data"]

    async def list_accounts(
        self, offset: int = 0, limit: int = 100
    ) -> List[Dict[str, Any]]:
        if offset < 0:
            raise ValueError("Offset must be non-negative")

        account_url = urljoin(self.base_url, "account/accounts")
        params = {"range": f'{{"offset":{offset},"limit":{limit}}}'}
        try:
            async with httpx.AsyncClient(verify=False) as client:
                r = await client.get(account_url, params=params, headers=self.headers)
                OBSClient.check_error(r)
        except Exception as e:
            logger.error(f"Failed to list Huawei OBS accounts: {str(e)}")
            raise e

        return r.json()["data"]

    async def get_accountid(self, account_name: str) -> Optional[str]:
        account_url = urljoin(self.base_url, "account/accounts")
        payload = {"name": account_name}
        try:
            async with httpx.AsyncClient(verify=False) as client:
                r = await client.get(account_url, params=payload, headers=self.headers)
                OBSClient.check_error(r)
        except Exception as e:
            logger.error(f"Failed to get Huawei OBS account: {str(e)}")
            raise e

        if r.json()["data"] == {}:
            logger.error(f"{r.json()['result']['description']}")
            return None

        return r.json()["data"]["id"]

    async def _delete_unix_users(self, account_name: str) -> None:
        users_url = urljoin(self.base_url, "nas_protocol/unix_user")
        params = f"account_name={account_name}" + '&range={"offset": 0, "limit": 100}'
        async with httpx.AsyncClient(verify=False) as client:
            r = await client.get(users_url, params=params, headers=self.headers)

            for user in r.json()["data"]:
                user_name = user["name"]
                delete_params = f"name={user_name}&account_name={account_name}"
                try:
                    r = await client.delete(
                        users_url, params=delete_params, headers=self.headers
                    )
                    OBSClient.check_error(r)
                except Exception as e:
                    logger.error(
                        f"Failed to delete user {user_name} for account {account_name}: {str(e)}"
                    )
                    raise e

    async def _delete_unix_groups(self, account_name: str) -> None:
        groups_url = urljoin(self.base_url, "nas_protocol/unix_group")
        params = f"account_name={account_name}" + '&range={"offset": 0, "limit": 100}'
        async with httpx.AsyncClient(verify=False) as client:
            r = await client.get(groups_url, params=params, headers=self.headers)

            for group in r.json()["data"]:
                group_name = group["name"]
                delete_params = f"name={group_name}&account_name={account_name}"
                try:
                    r = await client.delete(
                        groups_url, params=delete_params, headers=self.headers
                    )
                    OBSClient.check_error(r)
                except Exception as e:
                    logger.error(
                        f"Failed to delete group {group_name} for account {account_name}: {str(e)}"
                    )
                    raise e

    async def delete_account(self, account_name: str) -> None:
        await self._delete_unix_users(account_name)
        await self._delete_unix_groups(account_name)

        account_url = urljoin(self.base_url, "account/accounts")
        payload = {"name": account_name}
        async with httpx.AsyncClient(verify=False) as client:
            r = await client.delete(account_url, headers=self.headers, json=payload)  # type: ignore
            OBSClient.check_error(r)

    async def create_account_administrator(
        self, account_id: str, password: str
    ) -> None:
        base_url = f"{'https' if self.use_https else 'http'}://{self.host}:{self.port}/"
        admin_url = urljoin(base_url, f"deviceManager/rest/{self.esn}/user")
        payload = {
            "LOGINMETHODLIST": "3,4",  # GUI, RESTFULL
            "NAME": "administrator",
            "DESCRIPTION": "Administrator",
            "PASSWORD": password,
            "ROLEID": "1024",  # Administrator
            "SCOPE": 0,
            "account_id": account_id,
        }
        async with httpx.AsyncClient(verify=False) as client:
            r = await client.post(admin_url, headers=self.headers, json=payload)
            OBSClient.check_error(r)

    async def get_metrics(
        self,
        instance_type: Instance,
        object_name: str,
        human: bool,
        selector: Optional[List[str]] = None,
        custom_time: Optional[Dict[str, Any]] = None,
    ) -> List[Dict[str, Any]]:
        if instance_type is Instance.ACCOUNT:
            metrics = await self.get_account_metrics(account_id=object_name)
        else:
            raise NotImplementedError(
                f"Metrics for {instance_type.value} not yet implemented"
            )

        if selector not in ["current"]:
            raise NotImplementedError(
                f"Timeframe {selector} not yet implemented for Huawei OBS"
            )

        response = [
            {
                "interval": "current",
                "startDate": "2024-01-01T00:00:00Z",
                "endDate": "2024-01-01T01:00:00Z",
                "storageUtilized": humanfriendly.format_size(
                    abs(metrics.SpaceSize), binary=True
                )
                if human
                else metrics.SpaceSize,
                "numberOfObjects": metrics.ObjectCount,
                "numberOfOperations": {},
            }
        ]
        return response

    async def get_account_metrics(self, account_id: str) -> OBSAccountMetricsResponse:
        account_url = urljoin(
            self.base_url, f"dfv/service/obsPOE/accountStatistic/{account_id}"
        )
        async with httpx.AsyncClient(verify=False) as client:
            r = await client.get(account_url, headers=self.headers)
            OBSClient.check_error(r)

            #  .... r.json()["data"] = {'BucketCount': 5,
            #                           'GlobalBucketCount': 5,
            #                           'GlobalObjectCount': 3455,
            #                           'GlobalSpaceSize': *********,
            #                           'ObjectCount': 3455,
            #                           'Quota': 0,
            #                           'SpaceSize': *********}
            response = OBSAccountMetricsResponse(
                BucketCount=r.json()["data"]["BucketCount"],
                GlobalBucketCount=r.json()["data"]["GlobalBucketCount"],
                GlobalObjectCount=r.json()["data"]["GlobalObjectCount"],
                GlobalSpaceSize=r.json()["data"]["GlobalSpaceSize"],
                ObjectCount=r.json()["data"]["ObjectCount"],
                Quota=r.json()["data"]["Quota"],
                SpaceSize=r.json()["data"]["SpaceSize"],
            )
            return response

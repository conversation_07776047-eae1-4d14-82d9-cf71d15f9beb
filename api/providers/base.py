from abc import ABC, abstractmethod
from enum import Enum
from typing import Any, Dict, List, Optional

from ..models import AccountInstanceResponse


class Instance(str, Enum):
    """Instance types for metrics"""

    ACCOUNT = "account"
    USER = "user"
    BUCKET = "bucket"


class BaseAccountProvider(ABC):
    """Abstract base class for account management providers"""

    def __init__(self, region: str, config: Dict[str, str]):
        self.region = region
        self.config = config

    @abstractmethod
    async def create_account(
        self,
        account_name: str,
        account_id: Optional[str],
        email: str,
        quota: int,
    ) -> AccountInstanceResponse:
        pass

    @abstractmethod
    async def delete_account(self, account_name: str) -> None:
        pass

    @abstractmethod
    async def get_account(
        self, account_name: str, starts_with: bool = False, ends_with: bool = False
    ) -> List[Dict[str, Any]]:
        pass

    @abstractmethod
    async def generate_account_access_key(self, account_name: str) -> Dict[str, str]:
        pass

    @abstractmethod
    async def update_account_quota(self, account_name: str, quota_bytes: int) -> None:
        pass

    @abstractmethod
    async def delete_account_quota(self, account_name: str) -> None:
        pass

    @abstractmethod
    async def get_account_quota(self, account_name: str) -> Dict[str, Any]:
        pass


class BaseConsoleProvider(ABC):
    """Abstract base class for console providers"""

    def __init__(self, region: str, config: Dict[str, str]):
        self.region = region
        self.config = config

    @abstractmethod
    async def authenticate(self, console_username: str, console_password: str) -> None:
        pass

    @abstractmethod
    async def create_account(
        self, account_name: str, email: str, quota: int, password: Optional[str]
    ) -> AccountInstanceResponse:
        pass

    @abstractmethod
    async def delete_account(self, account_name: str) -> None:
        pass

    @abstractmethod
    async def delete_account_user(self, account_name: str) -> None:
        pass

    @abstractmethod
    async def get_account(self, account_name: str) -> Dict[str, Any]:
        pass


class BaseMetricsProvider(ABC):
    """Abstract base class for metrics providers"""

    def __init__(self, region: str, config: Dict[str, str]):
        self.region = region
        self.config = config

    @abstractmethod
    async def get_metrics(
        self,
        instance_type: Instance,
        object_name: str,
        human: bool,
        selector: Optional[List[str]] = None,
        custom_time: Optional[Dict[str, Any]] = None,
    ) -> List[Dict[str, Any]]:
        pass


class BaseS3Provider(ABC):
    """Abstract base class for S3 operations"""

    def __init__(self, region: str, config: Dict[str, str]):
        self.region = region
        self.config = config

    @abstractmethod
    def get_s3_client(self, access_key: str, secret_key: str) -> Any:
        """Get S3 client with the specified credentials"""
        pass

    @abstractmethod
    def get_iam_client(self, access_key: str, secret_key: str) -> Any:
        """Get IAM client with the specified credentials"""
        pass


class BaseIAMProvider(ABC):
    """Abstract base class for IAM operations"""

    def __init__(
        self, region: str, endpoint_url: str, access_key: str, secret_access_key: str
    ):
        self.region = region
        self.endpoint_url = endpoint_url
        self.access_key = access_key
        self.secret_access_key = secret_access_key

    @abstractmethod
    def remove_users_from_groups(self) -> None:
        pass

    @abstractmethod
    def detach_role_policies(self) -> None:
        pass

    @abstractmethod
    def detach_group_policies(self) -> None:
        pass

    @abstractmethod
    def detach_user_policies(self) -> None:
        pass

    @abstractmethod
    def delete_policy_versions(self) -> None:
        pass

    @abstractmethod
    def delete_policies(self) -> None:
        pass

    @abstractmethod
    def delete_roles(self) -> None:
        pass

    @abstractmethod
    def delete_groups(self) -> None:
        pass

    @abstractmethod
    def delete_users(self) -> None:
        pass

    @abstractmethod
    async def cleanup_account_iam(self, access_key: str, secret_key: str) -> None:
        """Clean up all IAM entities for an account"""
        pass

"""
Huawei Cloud provider implementation for S3-compatible services.

This module provides Huawei Cloud Object Storage Service (OBS) integration
following the same interface as other providers.
"""

import logging
from typing import Any, Dict, List, Optional

import boto3

from ..exceptions import NoSuchEntity
from ..models import AccountInstanceResponse, VaultAccountResponse
from .base import (
    BaseAccountProvider,
    BaseConsoleProvider,
    BaseIAMProvider,
    BaseMetricsProvider,
    BaseS3Provider,
    Instance,
)
from .huawei.obs_client import Instance as OBSInstance
from .huawei.obs_client import OBSClient
from .huawei.poe_client import POEClient

logger = logging.getLogger(__name__)


def generate_password():
    return "password"


class HuaweiAccountProvider(BaseAccountProvider):
    """Huawei Cloud implementation of account provider"""

    def __init__(self, region: str, config: Dict[str, str]):
        super().__init__(region, config)

        # Initialize Huawei OBS client
        self.poe_client = POEClient(
            endpoint=config["poe_endpoint"],
            port=config["poe_port"],
            use_https=False,
            access_key=config["poe_access_key"],
            secret_key=config["poe_secret_key"],
        )

    async def create_account(
        self, account_name: str, account_id: str, email: str, quota: int
    ) -> AccountInstanceResponse:
        """Create account using Huawei Cloud APIs"""

        logger.info(f"Creating Huawei account: {account_name}")

        r = await self.poe_client.create_account(
            account_name=account_name, account_id=account_id, email=email
        )
        await self.poe_client.set_quota_bytes(account_id=account_id, quota_bytes=quota)

        result: AccountInstanceResponse = AccountInstanceResponse(
            endpointUrl=self.config["s3_endpoint_url"],
            accountId=account_id,
            accountName=account_name,
            emailAddress=email,
            consolePassword="",
            accessKey=r["CreateAccountWithAllResponse"]["CreateAccessKeyResult"][
                "AccessKey"
            ]["AccessKeyId"],
            secretAccessKey=r["CreateAccountWithAllResponse"]["CreateAccessKeyResult"][
                "AccessKey"
            ]["SecretAccessKey"],
        )

        return result

    async def delete_account(self, account_name: str) -> None:
        """Delete account using Huawei Cloud APIs"""
        logger.info(f"Deleting Huawei account: {account_name}")
        await self.poe_client.delete_account(account_name=account_name)

    async def get_account(
        self, account_name: str, starts_with: bool = False, ends_with: bool = False
    ) -> List[VaultAccountResponse]:
        """Get account via Huawei Cloud console"""
        logger.info(f"Getting account via Huawei vault: {account_name}")
        accounts_list = []
        if not starts_with and not ends_with:
            account = await self.poe_client.get_account(account_name)
            if account:
                accounts_list.append(account)
        else:
            limit = 100
            offset = 0
            while True:
                accounts = await self.poe_client.list_accounts(
                    offset=offset, limit=limit
                )
                for account in accounts:
                    if starts_with and account["AccountName"].startswith(account_name):
                        accounts_list.append(account)
                    elif ends_with and account["AccountName"].endswith(account_name):
                        accounts_list.append(account)
                if len(accounts) < limit:
                    break
                offset += limit

        if len(accounts_list) == 0:
            raise NoSuchEntity("Missing account")

        vault_accounts = [
            VaultAccountResponse(
                arn=acc.get("Arn", ""),
                id=acc.get("AccountId", ""),
                name=acc.get("AccountName", ""),
                createDate=acc.get("CreateDate", ""),
                emailAddress=acc.get("Email", ""),
                canonicalId=acc.get("CanonicalUserId", ""),
                quota=0,  # Default value
            )
            for acc in accounts_list
        ]

        return vault_accounts

    async def generate_account_access_key(self, account_name: str) -> Dict[str, str]:
        """Generate access key for account using Huawei Cloud APIs"""
        logger.info(f"Generating access key for Huawei account: {account_name}")
        account_id = await self.poe_client.get_account_id(account_name)
        keys = await self.poe_client.create_ak(account_id=account_id)
        return {"accessKey": keys[0], "secretKeyValue": keys[1]}

    async def update_account_quota(self, account_name: str, quota_bytes: int) -> None:
        """Update account quota using Huawei Cloud APIs"""
        logger.info(
            f"Updating quota for Huawei account: {account_name} to {quota_bytes}"
        )
        account_id = await self.poe_client.get_account_id(account_name)
        await self.poe_client.set_quota_bytes(
            account_id=account_id, quota_bytes=quota_bytes
        )

    async def delete_account_quota(self, account_name: str) -> None:
        """Delete account quota using Huawei Cloud APIs"""
        logger.info(f"Deleting quota for Huawei account: {account_name}")
        await self.poe_client.set_quota_bytes(account_id=account_name, quota_bytes=0)

    async def get_account_quota(self, account_name: str) -> Dict[str, Any]:
        """Get account quota using Huawei Cloud APIs"""
        # TODO: Implement Huawei-specific quota retrieval
        logger.info(f"Getting quota for Huawei account: {account_name}")
        account_id = await self.poe_client.get_account_id(account_name)
        quota_bytes = await self.poe_client.get_quota_bytes(account_id=account_id)
        return {"quota": quota_bytes}


class HuaweiMetricsProvider(BaseMetricsProvider):
    """Huawei Cloud implementation of metrics provider"""

    def __init__(self, region: str, config: Dict[str, str]):
        super().__init__(region, config)

        self.poe_client = POEClient(
            access_key=self.config["poe_access_key"],
            secret_key=self.config["poe_secret_key"],
            endpoint=self.config["poe_endpoint"],
            port=self.config["poe_port"],
            use_https=False,
        )
        self.obs_client = OBSClient(
            host=self.config["obs_host"],
            port=self.config["obs_port"],
            use_https=True,
            username=self.config["obs_username"],
            password=self.config["obs_password"],
            esn=self.config["esn"],
        )

        # Mapping from base Instance enum to OBS Instance enum
        self._instance_mapping = {
            Instance.ACCOUNT: OBSInstance.ACCOUNT,
            Instance.USER: OBSInstance.USER,
            Instance.BUCKET: OBSInstance.BUCKET,
        }

    async def get_metrics(
        self,
        instance_type: Instance,
        object_name: str,
        human: bool,
        selector: Optional[List[str]] = None,
        custom_time: Optional[Dict[str, Any]] = None,
    ) -> List[Dict[str, Any]]:
        """Get metrics"""

        logger.info(f"Getting Huawei metrics for {instance_type.value}: {object_name}")

        if instance_type is Instance.ACCOUNT:
            object_name = await self.poe_client.get_account_id(object_name)

        # Convert base Instance enum to OBS Instance enum
        obs_instance_type = self._instance_mapping[instance_type]

        metrics = await self.obs_client.get_metrics(
            instance_type=obs_instance_type,
            object_name=object_name,
            human=human,
            selector=selector,
            custom_time=custom_time,
        )

        return metrics


class HuaweiConsoleProvider(BaseConsoleProvider):
    """Huawei Cloud implementation of console provider"""

    def __init__(self, region: str, config: Dict[str, str]):
        super().__init__(region, config)
        self.obs_client = OBSClient(
            host=config["obs_host"],
            port=config["obs_port"],
            use_https=True,
            username=config["obs_username"],
            password=config["obs_password"],
            esn=config["esn"],
        )

    async def create_account(
        self, account_name: str, email: str, quota: int, password: str
    ) -> AccountInstanceResponse:
        """Create account via Huawei Cloud console"""
        logger.info(f"Creating account via Huawei console: {account_name}")
        # TODO add quota
        response = await self.obs_client.create_account(account_name=account_name)
        await self.obs_client.create_account_administrator(
            account_id=response.accountId, password=password
        )
        response.consolePassword = password
        return response

    async def delete_account(self, account_name: str) -> None:
        """Delete account via Huawei Cloud console"""
        logger.info(f"Deleting account via Huawei console: {account_name}")
        await self.obs_client._delete_unix_users(account_name)
        await self.obs_client._delete_unix_groups(account_name)
        await self.obs_client.delete_account(account_name)

    async def delete_account_user(self, account_name: str) -> None:
        """Delete account user via Huawei Cloud console"""
        # TODO: Implement Huawei console user deletion
        logger.info(f"Deleting user via Huawei console: {account_name}")
        raise NotImplementedError("Huawei console user deletion not yet implemented")

    async def get_account(
        self, account_name: str, starts_with: bool = False, ends_with: bool = False
    ) -> List[Dict[str, Any]]:
        """Get account via Huawei Cloud console"""
        logger.info(f"Getting account via Huawei console: {account_name}")
        accounts_list = []
        if not starts_with and not ends_with:
            account = await self.obs_client.get_account(account_name)
            if account:
                accounts_list.append(account)
        else:
            limit = 100
            offset = 0
            while True:
                accounts = await self.obs_client.list_accounts(
                    offset=offset, limit=limit
                )
                for account in accounts:
                    if starts_with and account["name"].startswith(account_name):
                        accounts_list.append(account)
                    elif ends_with and account["name"].endswith(account_name):
                        accounts_list.append(account)
                if len(accounts) < limit:
                    break
                offset += limit

        if len(accounts_list) == 0:
            raise NoSuchEntity("Missing account")

        return accounts_list


class HuaweiS3Provider(BaseS3Provider):
    """Huawei Cloud implementation of S3 provider using OBS"""

    def __init__(self, region: str, config: Dict[str, str]):
        super().__init__(region, config)
        self.endpoint_url = config.get("obs_endpoint_url")
        self.access_key = config.get("obs_access_key")
        self.secret_key = config.get("obs_secret_key")

    def get_s3_client(self, access_key: str, secret_key: str):
        """Get S3 client configured for Huawei OBS"""
        return boto3.client(
            "s3",
            endpoint_url=self.endpoint_url,
            aws_access_key_id=access_key,
            aws_secret_access_key=secret_key,
            region_name=self.region,
            # Huawei OBS specific configurations
            # config=boto3.session.Config(
            #    signature_version="s3v4", s3={"addressing_style": "virtual"}
            # ),
        )


class HuaweiIAMProvider(BaseIAMProvider):
    """Huawei Cloud implementation of IAM provider"""

    def __init__(
        self, region: str, endpoint_url: str, access_key: str, secret_access_key: str
    ):
        super().__init__(region, endpoint_url, access_key, secret_access_key)
        # TODO: Initialize Huawei IAM client
        logger.info("Initializing Huawei IAM provider")

    async def create_user(self, username: str, password: str) -> Dict[str, Any]:
        """Create IAM user using Huawei Cloud APIs"""
        # TODO: Implement Huawei IAM user creation
        logger.info(f"Creating Huawei IAM user: {username}")
        raise NotImplementedError("Huawei IAM user creation not yet implemented")

    async def delete_user(self, username: str) -> None:
        """Delete IAM user using Huawei Cloud APIs"""
        # TODO: Implement Huawei IAM user deletion
        logger.info(f"Deleting Huawei IAM user: {username}")
        raise NotImplementedError("Huawei IAM user deletion not yet implemented")

    async def create_access_key(self, username: str) -> Dict[str, str]:
        """Create access key for IAM user using Huawei Cloud APIs"""
        # TODO: Implement Huawei IAM access key creation
        logger.info(f"Creating access key for Huawei IAM user: {username}")
        raise NotImplementedError("Huawei IAM access key creation not yet implemented")

    async def delete_access_key(self, username: str, access_key_id: str) -> None:
        """Delete access key for IAM user using Huawei Cloud APIs"""
        # TODO: Implement Huawei IAM access key deletion
        logger.info(f"Deleting access key for Huawei IAM user: {username}")
        raise NotImplementedError("Huawei IAM access key deletion not yet implemented")

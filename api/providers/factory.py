from ..config import Config
from .base import (
    BaseAccountProvider,
    BaseConsoleProvider,
    BaseIAMProvider,
    BaseMetricsProvider,
    BaseS3Provider,
)
from .huawei_provider import (
    HuaweiAccountProvider,
    HuaweiConsoleProvider,
    HuaweiIAMProvider,
    HuaweiMetricsProvider,
    HuaweiS3Provider,
)
from .scality_provider import (
    ScalityAccountProvider,
    ScalityConsoleProvider,
    ScalityIAMProvider,
    ScalityMetricsProvider,
    ScalityS3Provider,
)


class ProviderFactory:
    """Factory class for creating provider instances"""

    _providers = {
        "scality": {
            "account": ScalityAccountProvider,
            "metrics": ScalityMetricsProvider,
            "console": ScalityConsoleProvider,
            "s3": ScalityS3Provider,
            "iam": ScalityIAMProvider,
        },
        "huawei": {
            "account": HuaweiAccountProvider,
            "metrics": HuaweiMetricsProvider,
            "console": HuaweiConsoleProvider,
            "s3": HuaweiS3Provider,
            "iam": HuaweiIAMProvider,
        },
    }
    _instances = {}  # Cache for provider instances by region and type

    @classmethod
    def get_account_provider(cls, region: str) -> BaseAccountProvider:
        """Get account provider for the specified region"""
        cache_key = f"account_{region}"
        if cache_key in cls._instances:
            return cls._instances[cache_key]

        config = Config().get_config_parser()
        provider_type = config[region].get(
            "provider", "scality"
        )  # Default to scality for backward compatibility

        provider_class = cls._providers[provider_type]["account"]
        instance = provider_class(region, dict(config[region]))
        cls._instances[cache_key] = instance
        return instance

    @classmethod
    def get_metrics_provider(cls, region: str) -> BaseMetricsProvider:
        """Get metrics provider for the specified region"""
        cache_key = f"metrics_{region}"
        if cache_key in cls._instances:
            return cls._instances[cache_key]

        config = Config().get_config_parser()
        provider_type = config[region].get(
            "provider", "scality"
        )  # Default to scality for backward compatibility

        provider_class = cls._providers[provider_type]["metrics"]
        instance = provider_class(region, dict(config[region]))
        cls._instances[cache_key] = instance
        return instance

    @classmethod
    def get_console_provider(cls, region: str) -> BaseConsoleProvider:
        """Get console provider for the specified region"""
        cache_key = f"console_{region}"
        if cache_key in cls._instances:
            return cls._instances[cache_key]

        config = Config().get_config_parser()
        provider_type = config[region].get(
            "provider", "scality"
        )  # Default to scality for backward compatibility

        provider_class = cls._providers[provider_type]["console"]
        instance = provider_class(region, dict(config[region]))
        cls._instances[cache_key] = instance
        return instance

    @classmethod
    def get_s3_provider(cls, region: str) -> BaseS3Provider:
        """Get S3 provider for the specified region"""
        cache_key = f"s3_{region}"
        if cache_key in cls._instances:
            return cls._instances[cache_key]

        config = Config().get_config_parser()
        provider_type = config[region].get(
            "provider", "scality"
        )  # Default to scality for backward compatibility

        provider_class = cls._providers[provider_type]["s3"]
        instance = provider_class(region, dict(config[region]))
        cls._instances[cache_key] = instance
        return instance

    @classmethod
    def get_iam_provider(
        cls, region: str, endpoint_url: str, access_key: str, secret_access_key: str
    ) -> BaseIAMProvider:
        """Get IAM provider for the specified region"""
        cache_key = f"iam_{region}"
        if cache_key in cls._instances:
            return cls._instances[cache_key]

        config = Config().get_config_parser()
        provider_type = config[region].get(
            "provider", "scality"
        )  # Default to scality for backward compatibility

        provider_class = cls._providers[provider_type]["iam"]
        instance = provider_class(region, dict(config[region]))
        cls._instances[cache_key] = instance
        return instance

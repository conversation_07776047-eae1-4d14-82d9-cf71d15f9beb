from datetime import datetime
from typing import Dict, List, Literal, Optional

from pydantic import BaseModel, Extra, Field, constr

JSO_SERVICE_PATTERN = r"^TESTBOX10|(JSO|JFP)[A-Z0-9]{7}$"
ServiceId = constr(regex=JSO_SERVICE_PATTERN)


def convert_datetime(dt: datetime) -> str:
    return dt.strftime("%Y-%m-%dT%H:%M:%SZ")


class BackInTimeInstance(BaseModel):
    access_key: str = Field(..., description="Access Key")  # Mandatory
    secret_access_key: str = Field(..., description="Secret Access Key")  # Mandatory
    endpoint_url: str = Field(
        "https://s3.fr-lyo.freepro.com", description="Endpoint URL"
    )
    region: str = Field(default=None, description="Region")
    bucket: str = Field(..., description="Bucket name")
    prefix: str = Field(default="", description="Prefix")
    date: datetime = Field(..., description="Date & Time for back in time")
    index_prefix: str = Field(..., description="Prefix name of the index file")

    class Config:
        json_encoders = {datetime: convert_datetime}


class JobId(BaseModel):
    job_id: int = Field(description="ID of the Job created", gt=0)

    def __init__(self, job_id: int):
        super().__init__(job_id=job_id)


class JsoObjectsCleanupInfo(BaseModel):
    service_id: str = Field(  # type: ignore
        ...,
        description="JN Service Code for this JSO",
    )
    bucket: str = Field(..., description="Bucket name")
    state: str = Field(..., description="Cleanup state")
    status: dict = Field(..., description="Cleanup status")

    def __init__(self, service_id: str, bucket: str, state: str, status: dict):
        super().__init__(
            service_id=service_id, bucket=bucket, state=state, status=status
        )


class JsoBucketsCleanupInfo(BaseModel):
    service_id: str = Field(  # type: ignore
        ...,
        description="JN Service Code for this JSO",
    )
    account: str = Field(..., description="Account name")
    state: str = Field(..., description="Cleanup state")
    status: dict = Field(..., description="Cleanup status")

    def __init__(self, service_id: str, account: str, state: str, status: dict):
        super().__init__(
            service_id=service_id, account=account, state=state, status=status
        )


class JsoObjectsCleanupInstance(
    BaseModel, extra=Extra.forbid
):  # extra fields not permitted
    access_key: str = Field(..., description="Access Key to the bucket")  # Mandatory
    secret_access_key: str = Field(
        ..., description="Secret Access Key to the bucket"
    )  # Mandatory

    region: str = Field(..., description="Region")  # Mandatory
    prefix: str = Field(default="", description="Prefix")
    skip_current: bool = Field(
        default=False, description="Delete only non-current objects (and markers)"
    )
    skip_markers: bool = Field(
        default=False, description="Skip deletion of delete markers"
    )
    skip_objects: bool = Field(default=False, description="Skip deletion of objects")
    skip_mpus: bool = Field(default=False, description="Skip deletion of MPUs")
    orphans_only: bool = Field(
        default=False,
        description="Delete only orphaned delete markers (use in conjunction with skip_markers = False)",
    )
    dry_run: bool = Field(default=False, description="Execute in dry-run mode")
    page_size: int = Field(
        default=1000,
        description="Number of objects per requests (Max(default): 1000)",
        gt=0,
        le=1000,
    )
    keymarker: str = Field(
        default="", desciption="The key to start with when listing objects in a bucket"
    )


class AccountInstanceInputs(BaseModel):
    accountName: str = Field(..., description="Name for this account")
    accountId: Optional[str] = Field(
        default=None, description="ID for this account (optional)"
    )
    console: bool = Field(
        default=True, description="Is Scality S3 Console account needed?"
    )
    quota: int = Field(default=0, description="Quota for this account in bytes", ge=0)


class AccountInstanceResponse(BaseModel):
    endpointUrl: str = Field(..., description="S3 Endpoint URL")
    accountId: str = Field(..., description="Account ID for this account")
    accountName: str = Field(..., description="Name for this account")
    emailAddress: str = Field(..., description="Email address for this account")
    consolePassword: str = Field(..., description="Password to access the console")
    accessKey: str = Field(..., description="Account master access key")
    secretAccessKey: str = Field(..., description="Account master access secret key")


class Metrics(BaseModel):
    interval: Literal["current", "week", "month", "year", "custom"] = Field(
        ..., description="Interval of collect"
    )
    startDate: str = Field(..., description="Start date of the interval")
    endDate: str = Field(..., description="End date of the interval")
    storageUtilized: str = Field(..., description="Storage utilization (bytes)")
    numberOfObjects: str = Field(..., description="Number of objects in the bucket")
    incomingBytes: str = Field(..., description="Incoming bytes during the interval")
    outgoingBytes: str = Field(..., description="Outgoing bytes during the interval")
    operations_count: str = Field(
        ..., description="Total number of operations in the bucket during the interval"
    )
    numberOfOperations: Dict[str, int] = Field(
        ..., description="Detailed list of operations in the bucket during the interval"
    )


class MetricsResponse(BaseModel):
    accountId: str = Field(default=None, description="Id of the account")
    userId: str = Field(default=None, description="Name of the user")
    bucketName: str = Field(default=None, description="Name of the bucket")
    metrics: List[Metrics] = Field(..., description="List of metrics")


class VaultAccountResponse(BaseModel):
    arn: str
    id: str
    name: str
    createDate: str
    emailAddress: str
    canonicalId: str
    quota: int


class ConsoleAccountResponse(VaultAccountResponse):
    inDatabase: str


class QuotaResponse(BaseModel):
    canonicalId: Optional[str]
    arn: Optional[str]
    quota: int

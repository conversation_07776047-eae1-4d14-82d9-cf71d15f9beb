import asyncio
import functools
import logging
import time
from datetime import datetime
from multiprocessing import Array
from os import getenv

import aioprocessing  # type: ignore
import boto3  # type: ignore
import botocore.exceptions  # type: ignore
import sentry_sdk  # type: ignore
from aiobotocore.config import AioConfig  # type: ignore
from aiobotocore.session import get_session  # type: ignore
from botocore.client import Config  # type: ignore
from jnapi import Job as JobNoAsync  # type: ignore
from jnapi_async import Job, JobStates  # type: ignore
from jnapi_async.Module.helpers import JobRunner  # type: ignore
from sentry_sdk.integrations.fastapi import FastApiIntegration  # type: ignore
from sentry_sdk.integrations.logging import (  # type: ignore
    LoggingIntegration,
    ignore_logger,
)
from sentry_sdk.integrations.starlette import StarletteIntegration  # type: ignore
from types_aiobotocore_s3 import ListObjectVersionsPaginator  # type: ignore

from ..queues_names import JSO_OBJECTS_CLEANUP_QUEUE
from ..utils import get_api_session, get_api_session_no_async, get_logger

WORKERS = 10
RETRIES = 5
POLL_TIMER = 0.1  # Process polling timer (seconds)
STATS_LIMIT_TIMER = 60  # Limits stats output to n seconds between log lines
CONNECT_TIMEOUT = 5
READ_TIMEOUT = 300
MAX_ATTEMPTS = 5
TTR = READ_TIMEOUT * MAX_ATTEMPTS + 5

DELETED_DM_COUNT_INDEX = 0
DELETED_OBJECTS_COUNT_INDEX = 1
DELETED_BYTES_INDEX = 2
SKIP_DM_COUNT_INDEX = 3
SKIP_OBJECTS_COUNT_INDEX = 4
TOTAL_OBJECTS_COUNT_INDEX = 5

VERSIONS_KEY = "Versions"
DELETEMAKERS_KEY = "DeleteMarkers"
VERSION_ID_KEY = "VersionId"
KEY_KEY = "Key"
IS_LATEST_KEY = "IsLatest"
CONTENTS_KEY = "Contents"

logger = get_logger("jso_cleanup_bucket")


async def logstats(stats, logitems: dict, job: Job):
    """
    Wrapper function for logging statistics related to the deletion process of objects from an S3 bucket.

    Args:
        stats (multiprocessing.Array): A shared memory stats array containing deletion statistics.
        logitems (dict): An arbitrary list of additional items to log.
        job: The job object.

    Returns:
        None

    Example Usage:
        logstats(stats, {"final": False, "keymarker": marker}, job)

    Code Analysis:
        - Create a log object with information about the bucket, dry run status, and deletion statistics.
        - Iterate over the logitems and add them to the log object.
        - Replace the logs of the provided job object with the generated log object.
    """
    if stats is None:
        stats = Array("d", range(7))
        for i in range(7):
            stats[i] = 0

    _logobj = {
        "bucket": job.body["bucket"],
        "prefix": job.body["prefix"],
        "dry_run": job.body["dry_run"],
        "stats": {
            "deleted markers": int(stats[DELETED_DM_COUNT_INDEX]),
            "deleted objects": int(stats[DELETED_OBJECTS_COUNT_INDEX]),
            "deleted total": int(stats[DELETED_DM_COUNT_INDEX])
            + int(stats[DELETED_OBJECTS_COUNT_INDEX]),
            "deleted bytes": int(stats[DELETED_BYTES_INDEX]),
            "skipped markers": int(stats[SKIP_DM_COUNT_INDEX]),
            "skipped objects": int(stats[SKIP_OBJECTS_COUNT_INDEX]),
            "skipped total": int(
                stats[SKIP_DM_COUNT_INDEX] + stats[SKIP_OBJECTS_COUNT_INDEX]
            ),
            "total objects": int(stats[TOTAL_OBJECTS_COUNT_INDEX]),
        },
    }

    for item in logitems:
        _logobj[item] = logitems[item]

    try:
        await job.replace_logs(str(_logobj))
    except Exception:
        logger.exception("Failed to log statistics")


def renew_ttr(ttr: int):
    """
    A decorator that renews the time-to-run (TTR) for a job before executing the decorated function.

    Args:
        ttr (int): The new visibility timeout value in seconds for the job

    Returns:
        callable: A decorator function that wraps the target function

    The decorator performs these steps:
    1. Gets the job_id from the first argument of the wrapped function
    2. Creates a JobNoAsync instance with the job_id
    3. Updates the job's visibility timeout
    4. Executes the wrapped function with original arguments
    """

    def decorate(fn):
        @functools.wraps(fn)
        def wrapper(*args, **kwargs):
            job_id = args[0].job_id
            JobNoAsync(
                session=get_api_session_no_async(), job_id=job_id
            ).change_visibility_timeout(ttr)
            return fn(*args, **kwargs)

        return wrapper

    return decorate


def bucket_is_empty(job: Job):
    """
    Check if a bucket is empty.
    """
    config = Config(
        connect_timeout=CONNECT_TIMEOUT,
        read_timeout=READ_TIMEOUT,
        retries={"max_attempts": MAX_ATTEMPTS, "mode": "standard"},
        signature_version="v4",
    )
    client = boto3.client(
        "s3",
        config=config,
        endpoint_url=job.body["endpoint_url"],
        region_name=job.body["region"],
        aws_secret_access_key=job.body["secret_access_key"],
        aws_access_key_id=job.body["access_key"],
    )

    try:
        response = client.list_object_versions(Bucket=job.body["bucket"], MaxKeys=1)
        return "Versions" not in response and "DeleteMarkers" not in response
    except Exception:
        logger.exception("Error listing bucket")
        return False


def handle_objects(job: Job, ovs: dict, stats: dict, objs: dict) -> int:
    """
    Handle objects/versions if we're supposed to
    """
    to_delete_cnt = 0
    if job.body["skip_objects"]:
        try:
            versions_count = len(ovs[VERSIONS_KEY])
            stats[SKIP_OBJECTS_COUNT_INDEX] += versions_count
            stats[TOTAL_OBJECTS_COUNT_INDEX] += versions_count
        except KeyError:
            pass  # No objects in list

        return to_delete_cnt

    try:
        for ver in ovs[VERSIONS_KEY]:
            # Keep the latest version if instructed.
            if job.body["skip_current"] and ver[IS_LATEST_KEY]:
                stats[SKIP_OBJECTS_COUNT_INDEX] += 1  # skip count
                stats[TOTAL_OBJECTS_COUNT_INDEX] += 1
                continue

            objs["Objects"].append(
                {KEY_KEY: ver[KEY_KEY], VERSION_ID_KEY: ver[VERSION_ID_KEY]}
            )

            # Stats gathering
            try:
                stats[DELETED_BYTES_INDEX] += ver["Size"]  # append size
                stats[DELETED_OBJECTS_COUNT_INDEX] += 1  # object count
                stats[TOTAL_OBJECTS_COUNT_INDEX] += 1  # total count
            except Exception:
                logger.exception("Error comitting stats")
            to_delete_cnt += 1
    except KeyError:
        pass
    except Exception:
        logger.exception("An exception was thrown!")

    return to_delete_cnt


def is_orphan(job: Job, key: str) -> bool:
    versions = list(
        job.s3client.list_object_versions(Bucket=job.body["bucket"], Prefix=key).get(
            VERSIONS_KEY, []
        )
    )
    return len(versions) == 0


def should_skip_marker(job: Job, ver: dict) -> bool:
    """
    Determines whether a given object version should be skipped during the deletion process.

    Args:
        job (Job): The job object containing information about the S3 bucket, access credentials,
        and deletion parameters.
        ver (dict): The object version to be checked.

    Returns:
        bool: True if the object version should be skipped, False otherwise.
    """
    job_config = job.body

    # We want to skip the marker if:
    # we're not deleting all objects - that could expose objects that were hidding behind a DeleteMarker
    # OR we're not processing all objects but specified orphoans only and the DeleteMarker is not an orphan
    return (
        (
            job_config["skip_current"]
            and job_config["skip_objects"]
            and not job_config["orphans_only"]
        )
        or (job_config["orphans_only"] and not ver[IS_LATEST_KEY])
        or (job_config["orphans_only"] and not is_orphan(job, ver[KEY_KEY]))
    )


def handle_deletemarkers(job: Job, ovs: dict, stats, objs: dict) -> int:
    """
    Handle delete markers if we're supposed to.

    Inputs:
    - job: The job object containing information about the S3 bucket, access credentials, and deletion parameters.
    - ovs: A list of object versions to be deleted.
    - stats: An array to track deletion statistics.
    - objs: A dictionary to store the objects to be deleted.

    Returns:
    - The number of delete markers that were handled.

    This function is responsible for handling the deletion of delete markers from an S3 bucket.
    It checks the job configuration to determine whether delete markers should be skipped or deleted,
    and updates the deletion statistics accordingly.
    If delete markers should be deleted, it adds them to the list of objects to be deleted.
    """

    to_delete_cnt = 0
    if job.body["skip_markers"]:
        try:
            deletemarkers_count = len(ovs[DELETEMAKERS_KEY])
            stats[SKIP_DM_COUNT_INDEX] += deletemarkers_count
            stats[TOTAL_OBJECTS_COUNT_INDEX] += deletemarkers_count
        except KeyError:
            pass  # No delete markers in list

        return 0

    try:
        for ver in ovs[DELETEMAKERS_KEY]:
            if should_skip_marker(job, ver):
                stats[SKIP_DM_COUNT_INDEX] += 1
                stats[TOTAL_OBJECTS_COUNT_INDEX] += 1
                continue

            objs["Objects"].append(
                {KEY_KEY: ver[KEY_KEY], VERSION_ID_KEY: ver[VERSION_ID_KEY]}
            )

            stats[DELETED_DM_COUNT_INDEX] += 1
            stats[TOTAL_OBJECTS_COUNT_INDEX] += 1
            to_delete_cnt += 1
    except KeyError:
        pass
    except Exception:
        logger.exception("An exception was thrown!")

    return to_delete_cnt


def handle_deletion(job: Job, objs: dict) -> int:
    # Actual objects deletion occurs now

    if len(objs["Objects"]) == 0:
        return 0

    for attempt in range(RETRIES):
        try:
            result = job.s3client.delete_objects(Bucket=job.body["bucket"], Delete=objs)
            return len(result.get("Deleted", []))
        except Exception as e:
            if attempt == RETRIES - 1:
                logger.error(f"Failed to delete objects after {RETRIES} attempts: {e}")
                raise
            time.sleep(2**attempt)  # Exponential backoff

    return 0


@renew_ttr(ttr=TTR)
def _run_batch(job: Job, ovs: dict, stats: dict):
    """
    Page worker for object and marker removal

    Inputs:
    - job: The job object containing information about the S3 bucket, access credentials, and deletion parameters.
    - ovs: A list of object versions to be deleted.
    - stats: An array to track deletion statistics.

    Outputs:
    - None

    Summary:
    The `_run_batch` function is responsible for handling the deletion of objects and markers from an S3 bucket.
    It prepares a list of objects to be deleted, and sends a delete request to the S3 client.

    Example Usage:
    job = Job(...)
    ovs = {...}
    stats = Array("d", range(6))
    _run_batch(job, ovs, stats)

    """

    dry_run = job.body["dry_run"]
    objs = {"Objects": [], "Quiet": False}

    with sentry_sdk.start_transaction(op="task", name="_run_batch"):
        with sentry_sdk.start_span(name="handle_objects"):
            to_delete_cnt_objects = handle_objects(job, ovs, stats, objs)
        with sentry_sdk.start_span(name="handle_deletemarkers"):
            to_delete_cnt_dms = handle_deletemarkers(job, ovs, stats, objs)

        if not dry_run:
            to_delete_cnt = to_delete_cnt_objects + to_delete_cnt_dms
            with sentry_sdk.start_span(name="handle_deletion"):
                deleted_cnt = handle_deletion(job, objs)
                try:
                    if deleted_cnt != to_delete_cnt:
                        logger.error(
                            "WARNING: delete_objects() call only returned {0} deleted objects, {1} were queued".format(
                                deleted_cnt, to_delete_cnt
                            )
                        )
                except Exception:
                    pass  # Probably nothing to delete anyway

    logger.debug(
        "_run_batch.jobs {} for bucket: {} deleted: {} / processed: {}".format(
            job.job_id,
            job.body["bucket"],
            int(stats[DELETED_DM_COUNT_INDEX])
            + int(stats[DELETED_OBJECTS_COUNT_INDEX]),
            int(stats[TOTAL_OBJECTS_COUNT_INDEX]),
        )
    )


async def zap_objects(job: Job):
    """
    The zap_objects function is responsible for deleting objects from an S3 bucket.
    It uses pagination to retrieve object versions from the bucket and distributes
    the deletion tasks among multiple worker processes.
    The function keeps track of deletion statistics and logs them periodically.

    Flow
    - Initialize an array called stats to keep track of deletion statistics.
    - Create an S3 client using the provided job parameters.
    - Set up a paginator to retrieve object versions from the bucket.
    - Iterate over the paginated results.
    - Check if the maximum number of worker processes has been reached.
      If not, create a new worker process to handle the deletion task.
    - If a worker process has finished its task, replace it with a new one.
    - Periodically log the deletion statistics.
    - Wait for all worker processes to finish.
    - Log the final deletion statistics.

    Args:
        job (Job): The job object containing information about the bucket, prefix,
        and other parameters for the deletion process.
    """
    logger.debug(zap_objects.__name__)

    tasks: list = []

    # Delete stats array
    # 0: marker count
    # 1: object count
    # 2: size
    # 3: skipped markers
    # 4: skipped objects
    # 5: total objects

    stats = Array("d", range(7))
    for i in range(7):
        stats[i] = 0

    session = get_session()
    config = AioConfig(
        connect_timeout=CONNECT_TIMEOUT,
        read_timeout=READ_TIMEOUT,
        retries={"max_attempts": MAX_ATTEMPTS, "mode": "standard"},
        signature_version="v4",
    )
    async with session.create_client(
        "s3",
        config=config,
        endpoint_url=job.body["endpoint_url"],
        region_name=job.body["region"],
        aws_secret_access_key=job.body["secret_access_key"],
        aws_access_key_id=job.body["access_key"],
    ) as client:
        stats_t = [time.time(), time.time()]
        wrkr = 0
        pagesize = job.body["page_size"]

        paginator: ListObjectVersionsPaginator = client.get_paginator(
            "list_object_versions"
        )  # type: ignore
        async for ovs in paginator.paginate(
            Bucket=job.body["bucket"],
            Prefix=job.body["prefix"],
            KeyMarker=job.body["keymarker"],
            PaginationConfig={"PageSize": pagesize},
        ):  # type: ignore
            while True:
                if wrkr >= WORKERS:
                    await asyncio.sleep(POLL_TIMER)
                    stats_t[1] = time.time()
                    wrkr = 1

                if len(tasks) < WORKERS:
                    tasks.append(
                        aioprocessing.AioProcess(
                            target=_run_batch, args=(job, ovs, stats)
                        )
                    )
                    tasks[wrkr].start()
                    wrkr += 1
                    break

                # Finished process replenish slot:
                elif not tasks[wrkr].is_alive():
                    tasks[wrkr] = aioprocessing.AioProcess(
                        target=_run_batch, args=(job, ovs, stats)
                    )
                    tasks[wrkr].start()
                    wrkr += 1
                    break

                # We have a full process pool
                wrkr += 1

            # Since we're polling every 1/10 of a second and don't want to log
            # an entry for every process, limit logging with a timer.
            if stats_t[1] - stats_t[0] >= STATS_LIMIT_TIMER:
                stats_t = [time.time(), time.time()]
                if VERSIONS_KEY in ovs:
                    marker = ovs[VERSIONS_KEY][-1][KEY_KEY]
                elif DELETEMAKERS_KEY in ovs:
                    marker = ovs[DELETEMAKERS_KEY][-1][KEY_KEY]
                else:
                    marker = "?"
                await logstats(stats, {"final": False, "keymarker": marker}, job)

        for task in tasks:
            await task.coro_join()

        await logstats(stats, {"final": True}, job)


async def zap_objects_workaround(job: Job) -> bool:
    """
    The zap_objects_workaround function is responsible for deleting objects from an S3 bucket.
    It uses pagination to retrieve object versions from the bucket and distributes
    the deletion tasks among multiple worker processes.
    The function keeps track of deletion statistics and logs them periodically.
    """
    logger.debug(zap_objects_workaround.__name__)

    complete = await workaround_phase_1(job)
    if complete:
        await workaround_phase_2(job)
        return True

    return False


async def object_is_locked(client, bucket, object: dict) -> bool:
    try:
        response = await client.get_object_retention(
            Bucket=bucket,
            Key=object[KEY_KEY],
            VersionId=object[VERSION_ID_KEY],
        )
        retain_until_date = response["Retention"]["RetainUntilDate"]

        if retain_until_date > datetime.now().replace(tzinfo=retain_until_date.tzinfo):
            logger.warning(
                f"""
                Object {object[KEY_KEY]} is locked and retention period has not passed ({retain_until_date}).
                 Skipping deletion.
                """
            )
            return True
    except Exception as e:
        if "InvalidRequest" in str(e):
            # Bucket is missing Object Lock Configuration
            return False

        logger.error(f"Error checking object retention: {e}")

    return False


@renew_ttr(ttr=TTR)
async def _execute_batch_deletion(job: Job, client, delete_batch: dict):
    """
    Execute batch deletion of S3 objects with TTR renewal.

    Args:
        job (Job): The job object
        client: The S3 client
        delete_batch (dict): Batch of objects to delete
    """
    if delete_batch["Objects"]:
        await client.delete_objects(Bucket=job.body["bucket"], Delete=delete_batch)
        delete_batch["Objects"] = []


async def workaround_phase_1(job: Job) -> bool:
    """
    The phase 1 consists of deleting versioned objects without specifying the version ID so it creates a delete marker.
    Uses batch deletion for better performance.

    Args:
        job (Job): The job object containing information about the bucket, prefix,
        and other parameters for the deletion process.
    """
    if job.body["dry_run"]:
        return True

    session = get_session()
    config = AioConfig(
        connect_timeout=CONNECT_TIMEOUT,
        read_timeout=READ_TIMEOUT,
        retries={"max_attempts": MAX_ATTEMPTS, "mode": "standard"},
        signature_version="v4",
    )
    async with session.create_client(
        "s3",
        config=config,
        endpoint_url=job.body["endpoint_url"],
        region_name=job.body["region"],
        aws_secret_access_key=job.body["secret_access_key"],
        aws_access_key_id=job.body["access_key"],
    ) as client:
        pagesize = job.body["page_size"]
        delete_batch: dict = {"Objects": [], "Quiet": True}

        paginator: ListObjectVersionsPaginator = client.get_paginator(
            "list_object_versions"
        )  # type: ignore
        async for ovs in paginator.paginate(
            Bucket=job.body["bucket"],
            Prefix=job.body["prefix"],
            KeyMarker=job.body["keymarker"],
            PaginationConfig={"PageSize": pagesize},
        ):  # type: ignore
            for object in ovs.get(VERSIONS_KEY, []):
                if object.get(IS_LATEST_KEY, True):
                    is_locked = await object_is_locked(
                        client, bucket=job.body["bucket"], object=object
                    )
                    if is_locked:
                        return False  # job cannot be completed

                    delete_batch["Objects"].append({"Key": object[KEY_KEY]})

                    if len(delete_batch["Objects"]) >= 1000:
                        await _execute_batch_deletion(job, client, delete_batch)

            # Process remaining objects in batch
            if not job.body["dry_run"]:
                await _execute_batch_deletion(job, client, delete_batch)

    return True


async def workaround_phase_2(job: Job):
    """
    The phase 2 consists of deleting all remaining objects.

    Args:
        job (Job): The job object containing information about the bucket, prefix,
        and other parameters for the deletion process.
    """
    if job.body["dry_run"]:
        return

    session = get_session()
    config = AioConfig(
        connect_timeout=CONNECT_TIMEOUT,
        read_timeout=READ_TIMEOUT,
        retries={"max_attempts": MAX_ATTEMPTS, "mode": "standard"},
        signature_version="v4",
    )
    async with session.create_client(
        "s3",
        config=config,
        endpoint_url=job.body["endpoint_url"],
        region_name=job.body["region"],
        aws_secret_access_key=job.body["secret_access_key"],
        aws_access_key_id=job.body["access_key"],
    ) as client:
        pagesize = job.body["page_size"]
        delete_batch: dict = {"Objects": [], "Quiet": True}

        paginator: ListObjectVersionsPaginator = client.get_paginator(
            "list_object_versions"
        )  # type: ignore
        async for ovs in paginator.paginate(
            Bucket=job.body["bucket"],
            Prefix=job.body["prefix"],
            KeyMarker=job.body["keymarker"],
            PaginationConfig={"PageSize": pagesize},
        ):  # type: ignore
            for object in ovs.get(VERSIONS_KEY, []):
                delete_batch["Objects"].append(
                    {"Key": object[KEY_KEY], "VersionId": object[VERSION_ID_KEY]}
                )
                if len(delete_batch["Objects"]) >= 1000:
                    await _execute_batch_deletion(job, client, delete_batch)

            for object in ovs.get(DELETEMAKERS_KEY, []):
                delete_batch["Objects"].append(
                    {"Key": object[KEY_KEY], "VersionId": object[VERSION_ID_KEY]}
                )
                if len(delete_batch["Objects"]) >= 1000:
                    await _execute_batch_deletion(job, client, delete_batch)

            # Process remaining objects in batch
            if delete_batch["Objects"]:
                await _execute_batch_deletion(job, client, delete_batch)


async def zap_mpus(job: Job):
    """
    Clean up any dangling MPUs.
    Exceptions will be catched by the caller.
    """
    logger.debug(zap_mpus.__name__)

    session = get_session()
    config = AioConfig(
        connect_timeout=CONNECT_TIMEOUT,
        read_timeout=READ_TIMEOUT,
        retries={"max_attempts": MAX_ATTEMPTS, "mode": "standard"},
        signature_version="v4",
    )
    async with session.create_client(
        "s3",
        config=config,
        endpoint_url=job.body["endpoint_url"],
        region_name=job.body["region"],
        aws_secret_access_key=job.body["secret_access_key"],
        aws_access_key_id=job.body["access_key"],
    ) as client:
        paginator = client.get_paginator("list_multipart_uploads")
        async for mpus in paginator.paginate(
            Bucket=job.body["bucket"], Prefix=job.body["prefix"]
        ):  # type: ignore
            if "Uploads" in mpus:
                for mpu in mpus["Uploads"]:
                    if job.body["dry_run"]:
                        break
                    await client.abort_multipart_upload(
                        Bucket=job.body["bucket"],
                        Key=mpu[KEY_KEY],
                        UploadId=mpu["UploadId"],
                    )  # type: ignore


class JsoObjectsCleanupRunner(JobRunner):
    async def job_processor(self, job: Job) -> bool:
        logger.info(f"Received cleanup bucket request for service {job.service_id}")
        logger.debug(f"Job to process : {job}")

        dry_run = job.body["dry_run"]
        skip_objects = job.body["skip_objects"]
        skip_markers = job.body["skip_markers"]
        skip_mpus = job.body["skip_mpus"]
        skip_current = job.body["skip_current"]

        if not dry_run:
            await job.replace_logs("Starting objects deletion...")
        else:
            await job.replace_logs("Starting objects deletion (dry-run mode)...")

        config = Config(
            connect_timeout=CONNECT_TIMEOUT,
            read_timeout=READ_TIMEOUT,
            retries={"max_attempts": MAX_ATTEMPTS, "mode": "standard"},
            signature_version="v4",
        )
        s3client = boto3.client(
            "s3",
            config=config,
            endpoint_url=job.body["endpoint_url"],
            region_name=job.body["region"],
            aws_secret_access_key=job.body["secret_access_key"],
            aws_access_key_id=job.body["access_key"],
        )
        job.s3client = s3client

        try:
            if not (skip_objects and skip_markers):
                await zap_objects(job)
                if (
                    not skip_objects
                    and not skip_markers
                    and not skip_current
                    and not bucket_is_empty(job)
                ):
                    # If we are emptying the bucket, apply the workaround to delete the lasts remaining objects
                    # due to Scality bug
                    complete = await zap_objects_workaround(job)
                    if not complete:
                        await job.replace_logs(
                            "Objects deletion failed: objects are still locked"
                        )
                        await job.set_state(JobStates.FAILURE)
                        return True
            if not skip_mpus:
                await zap_mpus(job)
            if bucket_is_empty(job):
                bucket = job.body["bucket"]
                logger.info(f"Bucket {bucket} is empty, job successful")
            if skip_objects and skip_markers and skip_mpus:
                await logstats(None, {"final": True}, job)
        except botocore.exceptions.ClientError as e:
            if e.response["Error"]["Code"] == "NoSuchBucket":
                await job.set_state(JobStates.SUCCESS)
                logger.info(f"Job {job.job_id} successful")
            else:
                logger.exception("Failed to delete objects: " + str(e))
                # The job provisioner has failed so record the failure to the job database.
                await job.replace_logs(f"Objects deletion failed: {str(e)}")
                await job.set_state(JobStates.FAILURE)
                # If we cannot retry to process the Job, set job state to FAILURE and return True to Ack the Job
            return True
        except Exception as e:
            logger.exception("Failed to delete objects: " + str(e))
            # The job provisioner has failed so record the failure to the job database.
            await job.add_log(f"Objects deletion failed: {str(e)}")
            await job.set_state(JobStates.FAILURE)
            # If we cannot retry to process the Job, set job state to FAILURE and return True to Ack the Job
            return True
        else:
            await job.set_state(JobStates.SUCCESS)
            logger.info(f"Job {job.job_id} successful")

        return True


async def main():
    sess = await get_api_session()

    __sentry_project = getenv("SENTRY_PROJECT")
    __sentry_dsn = getenv("SENTRY_DSN")
    __version = getenv("VERSION", "dev")
    __environment = getenv("ENVIRONMENT", "poc")
    if __sentry_project is not None and __sentry_dsn is not None:
        sentry_sdk.init(
            dsn=__sentry_dsn,
            traces_sample_rate=1.0,
            release=f"{__sentry_project}@{__version}",
            environment=__environment,
            enable_tracing=True,
            sample_rate=1.0,
            profiles_sample_rate=1.0,
            shutdown_timeout=10,
            integrations=[
                StarletteIntegration(transaction_style="endpoint"),
                FastApiIntegration(transaction_style="endpoint"),
                LoggingIntegration(
                    level=logging.INFO,  # Capture info and above as breadcrumbs
                    event_level=logging.WARNING,  # Send warnings as events
                ),
            ],
        )
        # Ignore jnapi_async to avoid sentry issues
        ignore_logger("jnapi_async")

        sentry_sdk.set_tag("service", "objects_cleanup_runner")

    logger.info("Job runner is ready.")
    runner = JsoObjectsCleanupRunner(session=sess)
    runner.bind(queue=JSO_OBJECTS_CLEANUP_QUEUE)
    await runner.run(ttr=TTR)


if __name__ == "__main__":
    asyncio.run(main(), debug=False)

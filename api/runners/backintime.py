import asyncio
import json
import logging
import os
from datetime import datetime
from operator import itemgetter
from os import getenv

import aioboto3  # type: ignore
import humanfriendly  # type: ignore
import pytz  # type: ignore
import sentry_sdk  # type: ignore
from aiobotocore.config import AioConfig  # type: ignore
from jnapi_async import Job, JobStates  # type: ignore
from jnapi_async.Module.helpers import Job<PERSON>unner  # type: ignore
from sentry_sdk.integrations.fastapi import FastApiIntegration  # type: ignore
from sentry_sdk.integrations.logging import (  # type: ignore
    LoggingIntegration,
    ignore_logger,
)
from sentry_sdk.integrations.starlette import StarletteIntegration  # type: ignore

from ..queues_names import JSO_BACKINTIME_QUEUE
from ..utils import get_api_session, get_logger

BASE_DIR = "/tmp"
TTR = 3600 * 4  # 4 hours

utc = pytz.UTC

logger = get_logger("jso_backintime")

MAIN_INDEX_EXTENSION = ".out"


async def generate_and_upload_cancel_index(job: Job):
    """This code is expected to write a file to the local filesystem containing the string "CA<PERSON>ELLED",
    then it uploads the file to S3 using the provided credentials and endpoint, and finally
    it deletes the local file."""

    index_prefix = job.body["index_prefix"]
    bucket = job.body["bucket"]
    filename = f"{BASE_DIR}/{index_prefix}{MAIN_INDEX_EXTENSION}"
    out_key = index_prefix + MAIN_INDEX_EXTENSION

    try:
        with open(filename, "w") as fp:
            # The CANCELLED keyword will be handled by jnas to stop its restoration process
            fp.write("CANCELLED\n")

        config = AioConfig(
            connect_timeout=5,
            read_timeout=5,
            retries={"max_attempts": 3, "mode": "standard"},
            signature_version="v4",
        )
        session = aioboto3.Session()
        async with session.client(
            "s3",
            aws_access_key_id=job.body["access_key"],
            aws_secret_access_key=job.body["secret_access_key"],
            endpoint_url=job.body["endpoint_url"],
            region_name=job.body.get("region", None),
            config=config,
        ) as s3:  # type: ignore
            try:
                await s3.upload_file(filename, bucket, out_key)
            except Exception as e:
                raise e
            finally:
                os.remove(filename)
    except Exception:
        pass


async def generate_and_upload_indexes(job: Job):
    """This code is expected to fetch metadata information from all objects in an S3 bucket,
    filter the versions by a specified date, sort them by datetime, and finally write the
    information to a file in the local filesystem."""

    error = False

    index_prefix = job.body["index_prefix"]
    bucket = job.body["bucket"]
    prefix = job.body["prefix"]
    sync_date = job.body["date"]
    main_index = f"{BASE_DIR}/{index_prefix}{MAIN_INDEX_EXTENSION}"

    config = AioConfig(
        connect_timeout=5,
        read_timeout=5,
        retries={"max_attempts": 3, "mode": "standard"},
        signature_version="v4",
    )
    session = aioboto3.Session()
    async with session.client(
        "s3",
        aws_access_key_id=job.body["access_key"],
        aws_secret_access_key=job.body["secret_access_key"],
        endpoint_url=job.body["endpoint_url"],
        region_name=job.body.get("region", None),
        config=config,
    ) as s3:  # type: ignore
        cnt = 0
        idx = 1
        total_objects = 0  # Number of objects to resync
        total_size = 0  # Total size of the objects to resync in bytes
        final_list = []  # List of objects to resync

        try:
            # build dict with all versions to consider
            d: dict = dict()
            paginator = s3.get_paginator("list_object_versions")
            async for object_response_itr in paginator.paginate(
                Bucket=bucket, Prefix=f"{prefix}/"
            ):
                # parse Versions and keep only the ones that are older than sync_date
                if "Versions" in object_response_itr:
                    lst = [
                        v
                        for v in object_response_itr["Versions"]
                        if v["LastModified"]
                        <= utc.localize(datetime.fromisoformat(sync_date))
                    ]

                    for elem in lst:
                        try:
                            d[elem["Key"]].append(
                                {
                                    "VersionId": elem["VersionId"],
                                    "LastModified": elem["LastModified"],
                                    "Size": elem["Size"],
                                    "Deleted": False,
                                }
                            )
                        except KeyError:
                            # key does not exist so create a new one
                            d[elem["Key"]] = [
                                {
                                    "VersionId": elem["VersionId"],
                                    "LastModified": elem["LastModified"],
                                    "Size": elem["Size"],
                                    "Deleted": False,
                                }
                            ]

                # parse Delemarkers and keep only the ones that are older than sync_date
                if "DeleteMarkers" in object_response_itr:
                    lst = [
                        v
                        for v in object_response_itr["DeleteMarkers"]
                        if v["LastModified"]
                        <= utc.localize(datetime.fromisoformat(sync_date))
                    ]

                    for elem in lst:
                        try:
                            d[elem["Key"]].append(
                                {
                                    "VersionId": elem["VersionId"],
                                    "LastModified": elem["LastModified"],
                                    "Deleted": True,
                                }
                            )
                        except KeyError:
                            d[elem["Key"]] = [
                                {
                                    "VersionId": elem["VersionId"],
                                    "LastModified": elem["LastModified"],
                                    "Deleted": True,
                                }
                            ]

            for k in d:
                # Iterates over the dictionary d, and for each key, sorts the values in the list
                # based on the "LastModified" datetime, And append the closest match to the
                # sync_date requested.

                sorted_list = sorted(d[k], key=itemgetter("LastModified"))

                # If closest match is not a "DeleteMarkers" then select it for recovery by
                # adding them to the index files.
                if not sorted_list[-1]["Deleted"]:
                    final_list.append(
                        {
                            "Key": k,
                            "VersionId": sorted_list[-1]["VersionId"],
                            "Size": sorted_list[-1]["Size"],
                            "LastModified": sorted_list[-1]["LastModified"].isoformat(),
                        }
                    )

                    logger.debug(
                        f"Adding {k}({sorted_list[-1]['VersionId']}) - \
                            {sorted_list[-1]['LastModified'].isoformat()} to the list"
                    )

                    total_size += sorted_list[-1]["Size"]
                    total_objects += 1
                    cnt += 1

                # Break index files by list of 1000 elements
                if cnt == 1000:
                    filename = f"{BASE_DIR}/{index_prefix}.{idx}"
                    with open(filename, "w") as fp:
                        logger.info(f"Creating index file: {filename}")
                        json.dump(final_list, fp)

                    out_key = f"{index_prefix}.{idx}"
                    try:
                        await s3.upload_file(filename, bucket, out_key)
                    except Exception as e:
                        raise e
                    finally:
                        os.remove(filename)

                    idx += 1
                    cnt = 0
                    final_list = []

            # handle remaining objects in the list
            if cnt > 0:
                filename = f"{BASE_DIR}/{index_prefix}.{idx}"
                with open(filename, "w") as fp:
                    logger.info(f"Creating index file: {filename}")
                    json.dump(final_list, fp)

                out_key = f"{index_prefix}.{idx}"
                try:
                    await s3.upload_file(filename, bucket, out_key)
                except Exception as e:
                    raise e
                finally:
                    os.remove(filename)
        except Exception as e:
            error = True
            raise e
        finally:
            # Try to upload main_index file in all situations so jnas is informed
            # although it is not appropriate to generate/handle exceptions in a
            # finally clause.
            with open(main_index, "w") as fp:
                if not error and total_objects > 0:
                    fp.write(f"TOTAL_SIZE: {total_size}\n")
                    for i in range(1, idx + 1):
                        line = f"{index_prefix}.{i}"
                        fp.write(f"{line}\n")
                else:
                    # If there is no objects to recover then let the index file empty.
                    pass

            # Do not handle exception here and let the caller process it as a complete failure
            out_key = index_prefix + MAIN_INDEX_EXTENSION
            try:
                await s3.upload_file(main_index, bucket, out_key)
            except Exception as e:
                logger.error("Back in time: Unable to upload main index")
                raise e
            finally:
                os.remove(main_index)

    await job.add_log(
        "Index generation complete: {} objects found for a size of {}".format(
            total_objects, humanfriendly.format_size(total_size, binary=True)
        )
    )


class BackInTimeRunner(JobRunner):
    async def kill_job_callback(self, job: Job) -> None:
        logger.info(
            f"Received kill signal for {job.job_id} on service {job.service_id}"
        )
        try:
            await generate_and_upload_cancel_index(job)
        except Exception:
            logger.exception("An Exception was caught!")

        return None

    async def job_processor(self, job: Job) -> bool:
        logger.info(f"Received back in time request for service {job.service_id}")
        logger.debug(f"Job to process : {job}")

        # This is not a provisioning job so don't save service

        try:
            await generate_and_upload_indexes(job)
        except Exception as e:
            logger.exception("Failed to create back in time indexes")

            # Record the error message to the job database.
            await job.add_log(f"Failed to create back in time indexes: {str(e)}")

            # The job provisioner has failed so record the failure to the job database.
            await job.set_state(JobStates.FAILURE)
        else:
            await job.set_state(JobStates.SUCCESS)
            logger.info(f"Job {job.job_id} successful")

        return True


async def main():
    sess = await get_api_session()

    __sentry_project = getenv("SENTRY_PROJECT")
    __sentry_dsn = getenv("SENTRY_DSN")
    __version = getenv("VERSION", "dev")
    __environment = getenv("ENVIRONMENT", "poc")
    if __sentry_project is not None and __sentry_dsn is not None:
        sentry_sdk.init(
            dsn=__sentry_dsn,
            traces_sample_rate=1.0,
            release=f"{__sentry_project}@{__version}",
            environment=__environment,
            enable_tracing=True,
            sample_rate=1.0,
            profiles_sample_rate=1.0,
            shutdown_timeout=10,
            integrations=[
                StarletteIntegration(transaction_style="endpoint"),
                FastApiIntegration(transaction_style="endpoint"),
                LoggingIntegration(
                    level=logging.INFO,  # Capture info and above as breadcrumbs
                    event_level=logging.WARNING,  # Send warnings as events
                ),
            ],
        )
        # Ignore jnapi_async to avoid sentry issues
        ignore_logger("jnapi_async")

        sentry_sdk.set_tag("service", "backintime_runner")

    logger.info("Job runner is ready.")

    runner = BackInTimeRunner(session=sess)
    runner.bind(queue=JSO_BACKINTIME_QUEUE)
    await runner.run(ttr=TTR)


if __name__ == "__main__":
    asyncio.run(main(), debug=False)

import asyncio
import logging
from os import getenv
from time import time

import sentry_sdk  # type: ignore
from aiobotocore.config import AioConfig  # type: ignore
from aiobotocore.session import get_session  # type: ignore
from jnapi_async import Job, JobKillType, Jobs, JobStates  # type: ignore
from jnapi_async import exceptions as JNAPI_exceptions  # type: ignore
from jnapi_async.Module.helpers import Job<PERSON>unner  # type: ignore
from scality.exceptions import NoSuchEntity  # type: ignore
from scality.vault import VaultClient  # type: ignore
from sentry_sdk.integrations.fastapi import FastApiIntegration  # type: ignore
from sentry_sdk.integrations.logging import (  # type: ignore
    LoggingIntegration,
    ignore_logger,
)
from sentry_sdk.integrations.starlette import StarletteIntegration  # type: ignore

from ..config import Config as Conf
from ..queues_names import JSO_ACCOUNT_CLEANUP_QUEUE, JSO_OBJECTS_CLEANUP_QUEUE
from ..utils import get_api_session, get_logger

CONNECT_TIMEOUT = 5
READ_TIMEOUT = 300
MAX_ATTEMPTS = 5
TTR = READ_TIMEOUT * MAX_ATTEMPTS + 5

logger = get_logger("jso_cleanup_buckets")
config = Conf().get_config_parser()
sess = None


async def renew_ttr(job: Job):
    # Extends the invisibility period for a job that is close to becoming visible again,
    # allowing the current worker more time to finish processing it.
    # It prevents another worker from picking up the job again mid-processing.
    # The key logic is checking the expiration and calling the API to renew the timeout as needed.
    if job.ttr_expiration - int(time()) < 30:
        await Job(session=sess, job_id=job.job_id).change_visibility_timeout(TTR)
        job.ttr_expiration = int(time()) + TTR
        logger.debug("TTR renewed")


async def job_monitor(current_job: Job, monitor_job_id: int) -> JobStates:
    await asyncio.sleep(1)
    jobs = Jobs(session=sess)
    monitor_job = await jobs.get_job(job_id=monitor_job_id)

    while monitor_job.state in (JobStates.PENDING, JobStates.STARTED):
        try:
            await renew_ttr(current_job)
        except Exception:
            pass

        await asyncio.sleep(5)
        monitor_job = await jobs.get_job(job_id=monitor_job_id)

    return monitor_job.state


async def zap_buckets(job: Job):
    """The purpose of this function is to delete all buckets for a given account.

    It takes a job object as input, which contains information like the account,
    region, and credentials needed to access the buckets.

    The key steps are:
        - Get credentials
        - List buckets
        - Create object cleanup jobs
        - Monitor object cleanup jobs
        - Delete buckets if all jobs succeeded

    Args:
        job (Job): Job object

    Raises:
        JNAPI_exceptions.UnrecoverableError: When a cleanup job fails to complete.
    """
    global sess
    logger.debug(zap_buckets.__name__)

    account = job.body["account"]
    dry_run = job.body["dry_run"]
    region = job.body["region"]

    endpoint_url = config[region]["s3_endpoint_url"]
    vault_host = config[region]["vault_host"]
    vault_port = config[region]["vault_port"]
    vault_access_key = config[region]["vault_access_key"]
    vault_secret_key = config[region]["vault_secret_key"]

    vault_client = VaultClient(
        host=vault_host,
        port=int(vault_port),
        use_https=True,
        access_key=vault_access_key,
        secret_access_key=vault_secret_key,
    )

    try:
        root_keys = vault_client.generate_account_access_key(account)
    except NoSuchEntity:
        logger.debug("Entity does not exist: " + account)
        await job.add_log("Entity does not exist: " + account)
        return

    s3_session = get_session()
    s3_config = AioConfig(
        connect_timeout=CONNECT_TIMEOUT,
        read_timeout=READ_TIMEOUT,
        retries={"max_attempts": MAX_ATTEMPTS, "mode": "standard"},
        signature_version="v4",
    )
    async with s3_session.create_client(
        "s3",
        config=s3_config,
        endpoint_url=endpoint_url,
        aws_secret_access_key=root_keys["secretKeyValue"],
        aws_access_key_id=root_keys["accessKey"],
    ) as s3_client:
        response = await s3_client.list_buckets()
        if len(response["Buckets"]) == 0:
            logger.debug("No bucket to delete")
            await job.add_log("No bucket to delete")
            return

        job_id_list = []
        jobs = Jobs(session=sess)
        for bucket in response["Buckets"]:
            bucket_name = bucket["Name"]
            logger.debug(f"Bucket to delete : {bucket_name}")

            # Create a new job for each bucket
            payload = {
                "bucket": bucket_name,
                "endpoint_url": endpoint_url,
                "access_key": root_keys["accessKey"],
                "secret_access_key": root_keys["secretKeyValue"],
                "region": region,
                "prefix": "",
                "skip_current": False,
                "skip_markers": False,
                "skip_objects": False,
                "skip_mpus": False,
                "dry_run": dry_run,
                "page_size": 1000,
                "keymarker": "",
            }

            job_id = await jobs.create(
                queue=JSO_OBJECTS_CLEANUP_QUEUE,
                service_id="jso_" + bucket_name,
                service_type="JSO",
                data=payload,
            )
            job_id_list.append(job_id)
            logger.info(f"Created new job for bucket {bucket_name}: {job_id}")

        job.body["child_job_ids"] = job_id_list
        job_monitors = [
            job_monitor(current_job=job, monitor_job_id=job_id)
            for job_id in job_id_list
        ]
        values = await asyncio.gather(*job_monitors)

        if dry_run:
            logger.debug("No bucket to delete - dry-run mode")
            await job.add_log("No bucket to delete - dry-run mode")
            return

        if values.count(JobStates.FAILURE) > 0 or values.count(JobStates.KILLED) > 0:
            raise JNAPI_exceptions.UnrecoverableError()

        # Delete the buckets if no failure occured
        for bucket in response["Buckets"]:
            bucket_name = bucket["Name"]
            await s3_client.delete_bucket(Bucket=bucket_name)
            logger.debug("Bucket deleted: {}".format(bucket_name))
            await job.add_log("Bucket deleted: {}".format(bucket_name))


class JsoAccountCleanupRunner(JobRunner):
    async def kill_job_callback(self, job: Job) -> bool:
        """This function is used to kill child jobs associated with a parent job.

        It takes a single input parameter called job which contains information about the parent job.
        The function doesn't directly return any outputs. Instead, it calls other functions to kill the child jobs.

        Args:
            job (Job): Parent job object

        Returns:
            True
        """

        for child_job_id in job.body.get("child_job_ids", []):
            await Jobs(session=sess).kill_job(
                job_id=child_job_id, type=JobKillType.KILL
            )

        return True

    async def job_processor(self, job: Job) -> bool:
        logger.info(f"Received cleanup account request for service {job.service_id}")

        dry_run = job.body["dry_run"]

        # This is not a provisioning job so don't save service
        # service = JsoAccountCleanupRunner(**job.body)
        # await self.save_service(service.service_id, pre_serialize_model(service))

        if not dry_run:
            await job.replace_logs(f"Starting buckets deletion {job.body['account']}")
        else:
            await job.replace_logs(
                f"Starting buckets deletion for account: {job.body['account']} (dry-run mode)"
            )

        try:
            await zap_buckets(job)
        except JNAPI_exceptions.UnrecoverableError as e:
            # The job provisioner has failed so record the failure to the job database.
            await job.add_log(
                f"Buckets deletion failed for account: {job.body['account']}: {str(e)}"
            )
            await job.set_state(JobStates.FAILURE)
            # If we cannot retry to process the Job, set job state to FAILURE and return True to Ack the Job
            return True
        except Exception as e:
            logger.exception(
                f"Failed to delete buckets for account: {job.body['account']}: {str(e)}"
            )
            # The job provisioner has failed so record the failure to the job database.
            await job.add_log(
                f"Buckets deletion failed for account: {job.body['account']}: {str(e)}"
            )
            await job.set_state(JobStates.FAILURE)
            # If we cannot retry to process the Job, set job state to FAILURE and return True to Ack the Job
            return True
        else:
            await job.add_log(
                f"Buckets deletion completed for account: {job.body['account']}"
            )
            await job.set_state(JobStates.SUCCESS)
            logger.info(f"Job {job.job_id} successful")

        return True


async def main():
    global sess

    sess = await get_api_session()

    __sentry_project = getenv("SENTRY_PROJECT")
    __sentry_dsn = getenv("SENTRY_DSN")
    __version = getenv("VERSION", "dev")
    __environment = getenv("ENVIRONMENT", "poc")
    if __sentry_project is not None and __sentry_dsn is not None:
        sentry_sdk.init(
            dsn=__sentry_dsn,
            traces_sample_rate=1.0,
            release=f"{__sentry_project}@{__version}",
            environment=__environment,
            enable_tracing=True,
            sample_rate=1.0,
            profiles_sample_rate=1.0,
            shutdown_timeout=10,
            integrations=[
                StarletteIntegration(transaction_style="endpoint"),
                FastApiIntegration(transaction_style="endpoint"),
                LoggingIntegration(
                    level=logging.INFO,  # Capture info and above as breadcrumbs
                    event_level=logging.WARNING,  # Send warnings as events
                ),
            ],
        )
        # Ignore jnapi_async to avoid sentry issues
        ignore_logger("jnapi_async")

        sentry_sdk.set_tag("service", "buckets_cleanup_runner")

    logger.info("Job runner is ready.")
    runner = JsoAccountCleanupRunner(session=sess)
    runner.bind(queue=JSO_ACCOUNT_CLEANUP_QUEUE)
    await runner.run(ttr=TTR)


if __name__ == "__main__":
    asyncio.run(main(), debug=False)

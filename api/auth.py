import datetime
import hashlib
import hmac
from urllib.parse import quote, urlparse

import requests  # type: ignore


def sign(key, msg):
    return hmac.new(key, msg.encode("utf-8"), hashlib.sha256).digest()


def getSignatureKey(key, dateStamp, regionName, serviceName):
    kDate = sign(("AWS4" + key).encode("utf-8"), dateStamp)
    kRegion = sign(kDate, regionName)
    kService = sign(kRegion, serviceName)
    kSigning = sign(kService, "aws4_request")
    return kSigning


class Signaturev4(requests.auth.AuthBase):  # type: ignore
    def __init__(self, region: str, access_key: str, secret_access_key: str, host: str):
        self.access_key = access_key
        self.secret_access_key = secret_access_key
        self.host = host
        self.region = region
        self.service = "iam"
        self.token = None

    def __call__(self, r):
        r.headers.update(self.get_request_headers_handler(r))
        return r

    def get_request_headers_handler(self, r):
        return self.get_request_headers(
            r=r,
            access_key=self.access_key,
            secret_access_key=self.secret_access_key,
            token=self.token,
        )

    def get_request_headers(self, r, access_key, secret_access_key, token):
        t = datetime.datetime.utcnow()
        amzdate = t.strftime("%Y%m%dT%H%M%SZ")
        datestamp = t.strftime("%Y%m%d")

        canonical_uri = Signaturev4.get_canonical_path(r)

        canonical_querystring = Signaturev4.get_canonical_querystring(r)

        body = r.body if r.body else bytes()
        try:
            body = body.encode("utf-8")  # type: ignore
        except (AttributeError, UnicodeDecodeError):
            body = body

        payload_hash = hashlib.sha256(body).hexdigest()

        canonical_headers = (
            "host:"
            + self.host
            + "\n"
            + "x-amz-content-sha256:"
            + payload_hash
            + "\n"
            + "x-amz-date:"
            + amzdate
            + "\n"
        )
        if token:
            canonical_headers += "x-amz-security-token:" + token + "\n"

        signed_headers = "host;x-amz-content-sha256;x-amz-date"
        if token:
            signed_headers += ";x-amz-security-token"

        canonical_request = (
            r.method
            + "\n"
            + canonical_uri
            + "\n"
            + canonical_querystring
            + "\n"
            + canonical_headers
            + "\n"
            + signed_headers
            + "\n"
            + payload_hash
        )

        algorithm = "AWS4-HMAC-SHA256"
        credential_scope = (
            datestamp + "/" + self.region + "/" + self.service + "/" + "aws4_request"
        )
        string_to_sign = (
            algorithm
            + "\n"
            + amzdate
            + "\n"
            + credential_scope
            + "\n"
            + hashlib.sha256(canonical_request.encode("utf-8")).hexdigest()
        )

        signing_key = getSignatureKey(
            secret_access_key, datestamp, self.region, self.service
        )

        string_to_sign_utf8 = string_to_sign.encode("utf-8")
        signature = hmac.new(
            signing_key, string_to_sign_utf8, hashlib.sha256
        ).hexdigest()

        authorization_header = (
            algorithm
            + " "
            + "Credential="
            + access_key
            + "/"
            + credential_scope
            + ", "
            + "SignedHeaders="
            + signed_headers
            + ", "
            + "Signature="
            + signature
        )

        headers = {
            "Authorization": authorization_header,
            "x-amz-date": amzdate,
            "x-amz-content-sha256": payload_hash,
        }
        if token:
            headers["X-Amz-Security-Token"] = token
        return headers

    @classmethod
    def get_canonical_path(cls, r):
        parsedurl = urlparse(r.url)

        return quote(parsedurl.path if parsedurl.path else "/", safe="/-_.~")

    @classmethod
    def get_canonical_querystring(cls, r):
        canonical_querystring = ""

        parsedurl = urlparse(r.url)
        querystring_sorted = "&".join(sorted(parsedurl.query.split("&")))

        for query_param in querystring_sorted.split("&"):
            key_val_split = query_param.split("=", 1)

            key = key_val_split[0]
            if len(key_val_split) > 1:
                val = key_val_split[1]
            else:
                val = ""

            if key:
                if canonical_querystring:
                    canonical_querystring += "&"
                canonical_querystring += "=".join([key, val])

        return canonical_querystring

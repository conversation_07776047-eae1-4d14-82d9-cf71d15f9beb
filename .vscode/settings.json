{
    "python.analysis.diagnosticSeverityOverrides": {
        "reportMissingImports": "none",
        "reportMissingModuleSource": "none"
    },
    "[python]": {
        //"editor.defaultFormatter": "ms-python.black-formatter"
        "editor.defaultFormatter": "charliermarsh.ruff"
    },
    "flake8.args": [
        "--max-line-length=120",
        "--extend-ignore=E203"
    ],
    "editor.codeLens": false,
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.organizeImports": "explicit"
    },
    "python.analysis.typeCheckingMode": "basic",
    "python.testing.unittestArgs": [
        "-v",
        "-s",
        "./tests",
        "-p",
        "test_*.py"
    ],
    "python.testing.pytestEnabled": false,
    "python.testing.unittestEnabled": true,
    "python.linting.mypyPath": ".venv/bin/mypy",
    "python.linting.mypyArgs": [
        "--follow-imports=error",
        "--ignore-missing-imports",
        "--show-column-numbers",
        "--no-pretty"
    ],
    "python.linting.mypyCategorySeverity.note": "Error",
    "python.linting.mypyEnabled": true
}
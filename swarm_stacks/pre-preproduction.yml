version: "3.9"
x-jso-image: &jso_image gitlab.as30781.net:4567/jn-api/modules/jso/jso_module:[DEPLOY_TAG]
x-defaults: &defaults
  environment: &default_env
    CLIENT_ID: "${INTERNAL_CLIENT_ID}"
    CLIENT_SECRET: "${INTERNAL_CLIENT_SECRET}"
    GATEWAY_API_URL: "${GATEWAY_API_URL}"
    IDENTITY_PROVIDER_URL: "${IDENTITY_PROVIDER_URL}"
    IDENTITY_PROVIDER_REALM: "${IDENTITY_PROVIDER_REALM}"
    IDP_ROOT_URI: "${IDP_ROOT_URI}"
  networks: &default_networks
    - internal_network
  deploy: &default_deploy
    placement:
      constraints: [node.labels.modules == 1]
    restart_policy:
      condition: on-failure
      delay: 30s
      max_attempts: 3
      window: 60s
    update_config:
        parallelism: 1
        delay: 10s     
  logging: &default_logging
    driver: fluentd
    options:
      fluentd-async: "true"
  labels:  &default_labels
    - "io.portainer.accesscontrol.teams=BU-SH"

x-traefik-labels: &traefik-labels
  traefik.enable: "true"
  traefik.tags: "backend"
  traefik.http.middlewares.jso-prefix.stripprefix.prefixes: "/jso"
  traefik.http.routers.jso-api.entryPoints: "api"
  traefik.http.routers.jso-api.rule: "Host(`${JN_API_FQDN}`) && PathPrefix(`/jso`)"
  traefik.http.routers.jso-api.middlewares: "jso-prefix@swarm,redirect-redoc@file,redirect-swagger@file"
  traefik.http.routers.jso-api.tls.certResolver: "step-ca"
  traefik.http.routers.jso-api.tls.domains.0.main: "${JN_API_FQDN}"
  traefik.http.services.jso-api.loadbalancer.server.port: "80"

x-common-service: &common_service
  image: *jso_image
  environment:
    <<: *default_env
  deploy:
    <<: *default_deploy
    replicas: 1
  logging:
    <<: *default_logging
  networks:
    *default_networks
  labels:
    *default_labels

services:
  api:
    <<: *common_service
    deploy:
      <<: *default_deploy
      replicas: 1
      labels:
        <<: *traefik-labels
    logging:
      <<: *default_logging
      options:
        tag: docker.jso.api

  objects_cleanup:
    <<: *common_service
    command: python3 -m api.runners.objects_cleanup
    logging:
      <<: *default_logging
      options:
        tag: docker.jso.objects_cleanup

  buckets_cleanup:
    <<: *common_service
    command: python3 -m api.runners.buckets_cleanup
    logging:
      <<: *default_logging
      options:
        tag: docker.jso.buckets_cleanup

  backintime:
    <<: *common_service
    command: python3 -m api.runners.backintime
    logging:
      <<: *default_logging
      options:
        tag: docker.jso.backintime

networks:
    internal_network:


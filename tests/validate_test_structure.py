#!/usr/bin/env python3
"""
Validation script for provider abstraction test structure.

This script validates the test files without requiring external dependencies.
It checks for proper test structure, naming conventions, and completeness.
"""

import ast
import sys
from pathlib import Path
from typing import Dict


def analyze_test_file(file_path: Path) -> Dict:
    """Analyze a test file and extract information about test classes and methods."""
    try:
        with open(file_path, "r") as f:
            content = f.read()

        tree = ast.parse(content)

        classes = []
        imports = []

        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                methods = []
                for item in node.body:
                    if isinstance(
                        item, (ast.FunctionDef, ast.AsyncFunctionDef)
                    ) and item.name.startswith("test_"):
                        methods.append(item.name)

                classes.append(
                    {
                        "name": node.name,
                        "methods": methods,
                        "method_count": len(methods),
                    }
                )

            elif isinstance(node, ast.Import):
                for alias in node.names:
                    imports.append(alias.name)

            elif isinstance(node, ast.ImportFrom):
                module = node.module or ""
                for alias in node.names:
                    imports.append(f"{module}.{alias.name}")

        return {
            "file": file_path.name,
            "classes": classes,
            "imports": imports,
            "total_classes": len(classes),
            "total_methods": sum(c["method_count"] for c in classes),
        }

    except Exception as e:
        return {
            "file": file_path.name,
            "error": str(e),
            "classes": [],
            "imports": [],
            "total_classes": 0,
            "total_methods": 0,
        }


def validate_test_coverage() -> bool:
    """Validate that all provider classes have corresponding tests."""
    expected_provider_tests = {
        "TestProviderFactory",
        "TestScalityAccountProvider",
        "TestScalityMetricsProvider",
        "TestScalityConsoleProvider",
        "TestScalityS3Provider",
        "TestScalityIAMProvider",
        "TestProviderIntegration",
        "TestProviderErrorHandling",
    }

    expected_router_tests = {
        "TestVaultRouterProviderIntegration",
        "TestMetricsRouterProviderIntegration",
        "TestConsoleRouterProviderIntegration",
        "TestProviderDependencyInjection",
    }

    test_dir = Path(__file__).parent

    # Analyze provider tests
    provider_test_file = test_dir / "test_providers.py"
    provider_analysis = analyze_test_file(provider_test_file)

    # Analyze router tests
    router_test_file = test_dir / "test_router_providers.py"
    router_analysis = analyze_test_file(router_test_file)

    print("🔍 Test Coverage Analysis")
    print("=" * 50)

    # Check provider tests
    print(f"\n📋 Provider Tests ({provider_test_file.name})")
    print(f"   Classes: {provider_analysis['total_classes']}")
    print(f"   Methods: {provider_analysis['total_methods']}")

    provider_classes = {c["name"] for c in provider_analysis["classes"]}
    missing_provider_tests = expected_provider_tests - provider_classes
    extra_provider_tests = provider_classes - expected_provider_tests

    if missing_provider_tests:
        print(f"   ❌ Missing test classes: {missing_provider_tests}")
    else:
        print("   ✅ All expected provider test classes present")

    if extra_provider_tests:
        print(f"   ℹ️  Extra test classes: {extra_provider_tests}")

    # Check router tests
    print(f"\n📋 Router Tests ({router_test_file.name})")
    print(f"   Classes: {router_analysis['total_classes']}")
    print(f"   Methods: {router_analysis['total_methods']}")

    router_classes = {c["name"] for c in router_analysis["classes"]}
    missing_router_tests = expected_router_tests - router_classes
    extra_router_tests = router_classes - expected_router_tests

    if missing_router_tests:
        print(f"   ❌ Missing test classes: {missing_router_tests}")
    else:
        print("   ✅ All expected router test classes present")

    if extra_router_tests:
        print(f"   ℹ️  Extra test classes: {extra_router_tests}")

    # Detailed breakdown
    print("\n📊 Detailed Breakdown")
    print("-" * 30)

    for analysis in [provider_analysis, router_analysis]:
        print(f"\n{analysis['file']}:")
        for cls in analysis["classes"]:
            print(f"  • {cls['name']}: {cls['method_count']} tests")
            if cls["method_count"] == 0:
                print("    ⚠️  No test methods found!")

    # Overall status
    all_good = (
        len(missing_provider_tests) == 0
        and len(missing_router_tests) == 0
        and provider_analysis["total_methods"] > 0
        and router_analysis["total_methods"] > 0
    )

    print(
        f"\n{'✅' if all_good else '❌'} Overall Status: {'PASS' if all_good else 'NEEDS ATTENTION'}"
    )

    return all_good


def validate_test_structure() -> bool:
    """Validate the overall test structure and files."""
    test_dir = Path(__file__).parent

    required_files = [
        "test_providers.py",
        "test_router_providers.py",
        "run_provider_tests.py",
        "pytest.ini",
        "README_PROVIDER_TESTS.md",
    ]

    print("📁 Test Structure Validation")
    print("=" * 50)

    all_present = True

    for file_name in required_files:
        file_path = test_dir / file_name
        if file_path.exists():
            size = file_path.stat().st_size
            print(f"   ✅ {file_name} ({size} bytes)")
        else:
            print(f"   ❌ {file_name} - MISSING")
            all_present = False

    return all_present


def main():
    """Main validation function."""
    print("🧪 Provider Abstraction Test Suite Validation")
    print("=" * 60)

    structure_valid = validate_test_structure()
    coverage_valid = validate_test_coverage()

    print("\n" + "=" * 60)

    if structure_valid and coverage_valid:
        print("🎉 All validations passed! Test suite is ready.")
        return 0
    else:
        print("💥 Some validations failed. Please review the issues above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())

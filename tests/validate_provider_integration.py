#!/usr/bin/env python3
"""
Validation script for provider integration in routers.

This script validates that the routers are properly configured to use
the ProviderFactory instead of direct service instantiation.
"""

import ast
import sys
from pathlib import Path


def check_router_provider_usage(router_file: Path) -> dict:
    """Check if a router file properly uses ProviderFactory."""
    results = {
        "file": str(router_file),
        "imports_provider_factory": False,
        "uses_provider_factory": False,
        "provider_factory_calls": [],
        "direct_service_calls": [],
        "issues": [],
    }

    try:
        with open(router_file, "r") as f:
            content = f.read()

        # Parse the AST
        tree = ast.parse(content)

        # Check imports
        for node in ast.walk(tree):
            if isinstance(node, ast.ImportFrom):
                if node.module and "providers" in node.module:
                    for alias in node.names:
                        if alias.name == "ProviderFactory":
                            results["imports_provider_factory"] = True

        # Check for ProviderFactory usage
        for node in ast.walk(tree):
            if isinstance(node, ast.Attribute):
                if (
                    isinstance(node.value, ast.Name)
                    and node.value.id == "ProviderFactory"
                ):
                    results["uses_provider_factory"] = True
                    results["provider_factory_calls"].append(node.attr)

        # Check for direct service instantiation (potential issues)
        direct_services = ["VaultClient", "Console", "Utapi", "Iam"]
        for node in ast.walk(tree):
            if isinstance(node, ast.Call):
                if isinstance(node.func, ast.Name) and node.func.id in direct_services:
                    results["direct_service_calls"].append(node.func.id)
                    results["issues"].append(
                        f"Direct instantiation of {node.func.id} found"
                    )

    except Exception as e:
        results["issues"].append(f"Error parsing file: {str(e)}")

    return results


def main():
    """Main validation function."""
    print("🔍 Validating Provider Integration in Routers")
    print("=" * 50)

    # Get the project root
    project_root = Path(__file__).parent.parent
    routers_dir = project_root / "api" / "routers"

    if not routers_dir.exists():
        print(f"❌ Routers directory not found: {routers_dir}")
        sys.exit(1)

    # Router files to check
    router_files = [
        routers_dir / "vault.py",
        routers_dir / "console.py",
        routers_dir / "metrics.py",
    ]

    all_passed = True

    for router_file in router_files:
        if not router_file.exists():
            print(f"❌ Router file not found: {router_file}")
            all_passed = False
            continue

        print(f"\n📁 Checking {router_file.name}...")
        results = check_router_provider_usage(router_file)

        # Check results
        if results["imports_provider_factory"]:
            print("  ✅ Imports ProviderFactory")
        else:
            print("  ❌ Does not import ProviderFactory")
            all_passed = False

        if results["uses_provider_factory"]:
            print("  ✅ Uses ProviderFactory")
            print(
                f"     Methods called: {', '.join(set(results['provider_factory_calls']))}"
            )
        else:
            print("  ❌ Does not use ProviderFactory")
            all_passed = False

        if results["direct_service_calls"]:
            print(
                f"  ⚠️  Direct service calls found: {', '.join(set(results['direct_service_calls']))}"
            )

        if results["issues"]:
            print("  ❌ Issues found:")
            for issue in results["issues"]:
                print(f"     - {issue}")
            all_passed = False

    print("\n" + "=" * 50)
    if all_passed:
        print("✅ All routers properly integrated with ProviderFactory!")
        print("\n🎉 Provider abstraction integration is complete and validated!")
    else:
        print("❌ Some routers have integration issues")
        sys.exit(1)


if __name__ == "__main__":
    main()

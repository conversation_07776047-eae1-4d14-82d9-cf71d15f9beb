import ast
import os
import time
import unittest
import warnings
from http import HTTPStatus
from time import sleep

import boto3  # type: ignore
import requests  # type: ignore
from botocore.client import Config  # type: ignore
from botocore.exceptions import ClientError  # type: ignore
from dotenv import load_dotenv  # type: ignore
from requests.adapters import HTTPAdapter  # type: ignore
from requests.exceptions import HTTPError  # type: ignore
from requests.packages.urllib3.util.retry import Retry  # type: ignore
from retry import retry  # type: ignore

load_dotenv()

S3_URL = os.environ.get("S3_URL")
REGION = os.environ.get("REGION")
ACCESS_KEY = os.environ.get("ACCESS_KEY")
SECRET_KEY = os.environ.get("SECRET_KEY")
BUCKET = os.environ.get("BUCKET")
ACCOUNT = os.environ.get("ACCOUNT")
objects_delete_url = f"http://localhost:8071/bucket/{BUCKET}/objects/delete"
buckets_delete_url = f"http://localhost:8071/vault/account/{ACCOUNT}/buckets/delete"
buckets_delete_url_bad_entity = (
    f"http://localhost:8071/vault/account/{ACCOUNT}_bad/buckets/delete"
)
test_buckets = ["test-bucket-1", "test-bucket-2", "test-bucket-3"]

my_config = Config(
    region_name=REGION,
    signature_version="v4",
    retries={"max_attempts": 3, "mode": "standard"},
)

retry_strategy = Retry(
    total=3,  # Total number of retries (including the initial request)
    status_forcelist=[500, 423],  # HTTP status codes to retry
    backoff_factor=0.5,  # Factor by which the delay increases after each retry
    method_whitelist=["GET", "POST"],  # HTTP methods to retry
)

s3_client = boto3.client(
    "s3",
    aws_access_key_id=ACCESS_KEY,
    aws_secret_access_key=SECRET_KEY,
    endpoint_url=S3_URL,
    config=my_config,
)

s3_resource = boto3.resource(
    "s3",
    aws_access_key_id=ACCESS_KEY,
    aws_secret_access_key=SECRET_KEY,
    endpoint_url=S3_URL,
    config=my_config,
)

adapter = HTTPAdapter(max_retries=retry_strategy)
client: requests.sessions.Session = requests.Session()
client.mount("http://", adapter)
client.mount("https://", adapter)


def get_token(client_id: str, client_secret: str):
    endpoint = os.getenv("IDENTITY_PROVIDER_TOKEN_URL", "")

    if endpoint == "":
        return None

    headers = {
        "accept": "application/json",
        "Content-Type": "application/x-www-form-urlencoded",
    }
    payload = {
        "grant_type": "client_credentials",
        "client_id": client_id,
        "client_secret": client_secret,
    }
    try:
        r = client.post(
            endpoint,
            headers=headers,
            data=payload,
        )
        r.raise_for_status()
    except Exception as e:
        raise e

    return r.json()["access_token"]


def create_bucket(bucket_name=BUCKET):
    try:
        s3_client.create_bucket(Bucket=bucket_name)
    except ClientError as error:
        if error.response["Error"]["Code"] == "BucketAlreadyOwnedByYou":
            pass


def create_bucket_lock(bucket_name=BUCKET):
    try:
        s3_client.create_bucket(Bucket=bucket_name, ObjectLockEnabledForBucket=True)
        s3_client.put_object_lock_configuration(
            Bucket=bucket_name,
            ObjectLockConfiguration={
                "ObjectLockEnabled": "Enabled",
                "Rule": {"DefaultRetention": {"Mode": "GOVERNANCE", "Days": 1}},
            },
        )
    except ClientError as error:
        if error.response["Error"]["Code"] == "BucketAlreadyOwnedByYou":
            pass


def set_versioning(bucket_name=BUCKET):
    s3_client.put_bucket_versioning(
        Bucket=bucket_name,
        VersioningConfiguration={"Status": "Enabled"},
    )


def suspend_versioning(bucket_name=BUCKET):
    s3_client.put_bucket_versioning(
        Bucket=bucket_name,
        VersioningConfiguration={"Status": "Suspended"},
    )


def empty_bucket(bucket_name=BUCKET):
    bucket = s3_resource.Bucket(bucket_name)  # type: ignore
    try:
        for key in bucket.object_versions.all():
            key.delete()
    except Exception:
        pass


def empty_bucket_lock(bucket_name=BUCKET):
    bucket = s3_resource.Bucket(bucket_name)  # type: ignore
    try:
        for key in bucket.object_versions.all():
            key.delete(BypassGovernanceRetention=True)
    except Exception:
        pass


def delete_bucket(bucket_name=BUCKET):
    empty_bucket(bucket_name)
    s3_client.delete_bucket(
        Bucket=bucket_name,
    )


class TestBucketsCleanupLock(unittest.TestCase):
    def setUp(self):
        time.sleep(0.5)
        pass

    def tearDown(self):
        try:
            empty_bucket_lock(BUCKET)
            delete_bucket(BUCKET)
        except Exception:
            pass

    @classmethod
    def setUpClass(cls) -> None:
        warnings.filterwarnings(
            action="ignore", message="unclosed", category=ResourceWarning
        )
        try:
            empty_bucket_lock(BUCKET)
            delete_bucket(BUCKET)
        except Exception:
            pass

        cls.clientid = os.environ.get("CLIENT_ID_JSO", None)
        assert cls.clientid is not None, "CLIENT_ID_JSO not set"
        cls.clientsecret = os.environ.get("CLIENT_SECRET_JSO", None)
        assert cls.clientsecret is not None, "CLIENT_SECRET_JSO not set"
        cls.token = get_token(cls.clientid, cls.clientsecret)
        # assert cls.token is not None, "Failed to get token"

    def AccountIsEmpty(self) -> bool:
        try:
            s3_client.head_bucket(Bucket=BUCKET)
            return False
        except Exception:
            pass

        return True

    @retry(exceptions=(HTTPError), delay=1, tries=3)
    def monitor_job(self, job_id, url=buckets_delete_url) -> str:
        while True:
            sleep(1)
            r = client.get(f"{url}?job_id={job_id}")
            r.raise_for_status()

            if r.json()["state"] in ["SUCCESS", "FAILURE"]:
                break

        return r.json()["state"]

    def test_delete(self) -> None:
        create_bucket_lock(BUCKET)
        sleep(1)
        for i in range(1, 3):
            s3_client.put_object(Body=b"body", Bucket=BUCKET, Key=f"file{i}.txt")

        headers = {"Authorization": f"Bearer {self.token}"}
        r = client.post(
            buckets_delete_url,
            params={"region": REGION, "dry_run": False},
            headers=headers,
        )
        r.raise_for_status()

        job_id = r.json()["job_id"]
        state = self.monitor_job(job_id)

        self.assertEqual(state, "FAILURE")
        self.assertFalse(self.AccountIsEmpty())


class TestBucketsCleanup(unittest.TestCase):
    def setUp(self):
        time.sleep(0.5)
        pass

    def tearDown(self):
        for bucket_name in test_buckets:
            try:
                empty_bucket(bucket_name)
                delete_bucket(bucket_name)
            except Exception:
                pass

    @classmethod
    def setUpClass(cls) -> None:
        warnings.filterwarnings(
            action="ignore", message="unclosed", category=ResourceWarning
        )
        try:
            for bucket in test_buckets:
                delete_bucket(bucket)
        except Exception:
            pass

        cls.clientid = os.environ.get("CLIENT_ID_JSO", None)
        assert cls.clientid is not None, "CLIENT_ID_JSO not set"
        cls.clientsecret = os.environ.get("CLIENT_SECRET_JSO", None)
        assert cls.clientsecret is not None, "CLIENT_SECRET_JSO not set"
        cls.token = get_token(cls.clientid, cls.clientsecret)
        assert cls.token is not None, "Failed to get token"

    @classmethod
    def tearDownClass(cls) -> None:
        for bucket_name in test_buckets:
            try:
                empty_bucket(bucket_name)
                delete_bucket(bucket_name)
            except Exception:
                pass

    def AccountIsEmpty(self) -> bool:
        for bucket_name in test_buckets:
            try:
                s3_client.head_bucket(Bucket=bucket_name)
                return False
            except Exception:
                pass

        return True

    def assertBucketExists(self, bucket_name) -> bool:
        try:
            s3_client.head_bucket(Bucket=bucket_name)
        except Exception:
            self.assertTrue(False)

        return True

    @retry(exceptions=(HTTPError), delay=1, tries=3)
    def monitor_job(self, job_id, url=buckets_delete_url) -> str:
        while True:
            sleep(1)
            r = client.get(f"{url}?job_id={job_id}")
            r.raise_for_status()

            if r.json()["state"] in ["SUCCESS", "FAILURE"]:
                break

        return r.json()["state"]

    def test_delete_all(self) -> None:
        for bucket_name in test_buckets:
            create_bucket(bucket_name)
            sleep(1)
            for i in range(1, 3):
                s3_client.put_object(
                    Body=b"body", Bucket=bucket_name, Key=f"file{i}.txt"
                )

        headers = {"Authorization": f"Bearer {self.token}"}
        r = client.post(
            buckets_delete_url,
            params={"region": REGION, "dry_run": False},
            headers=headers,
        )
        r.raise_for_status()

        job_id = r.json()["job_id"]
        state = self.monitor_job(job_id)

        self.assertEqual(state, "SUCCESS")
        self.assertTrue(self.AccountIsEmpty())

    def test_delete_all_bad_region(self) -> None:
        headers = {"Authorization": f"Bearer {self.token}"}
        r = client.post(
            buckets_delete_url,
            params={"region": "bad_region", "dry_run": True},
            headers=headers,
        )

        self.assertEqual(r.status_code, HTTPStatus.UNPROCESSABLE_ENTITY)

    def test_delete_all_bad_entity(self) -> None:
        headers = {"Authorization": f"Bearer {self.token}"}
        r = client.post(
            buckets_delete_url_bad_entity,
            params={"region": REGION, "dry_run": True},
            headers=headers,
        )
        r.raise_for_status()

        job_id = r.json()["job_id"]
        state = self.monitor_job(job_id, url=buckets_delete_url_bad_entity)

        self.assertEqual(state, "SUCCESS")

    def test_delete_all_dry_run(self) -> None:
        for bucket_name in test_buckets:
            create_bucket(bucket_name)
            sleep(1)
            for i in range(1, 3):
                s3_client.put_object(
                    Body=b"body", Bucket=bucket_name, Key=f"file{i}.txt"
                )

        headers = {"Authorization": f"Bearer {self.token}"}
        r = client.post(
            buckets_delete_url,
            params={"region": REGION, "dry_run": True},
            headers=headers,
        )
        r.raise_for_status()

        job_id = r.json()["job_id"]
        state = self.monitor_job(job_id)
        self.assertEqual(state, "SUCCESS")
        for bucket_name in test_buckets:
            self.assertBucketExists(bucket_name)


class TestObjectsCleanup(unittest.TestCase):
    def setUp(self):
        time.sleep(1)
        pass

    def tearDown(self):
        empty_bucket()

    @classmethod
    def setUpClass(cls) -> None:
        warnings.filterwarnings(
            action="ignore", message="unclosed", category=ResourceWarning
        )
        try:
            delete_bucket()
        except Exception:
            pass
        create_bucket()

    @classmethod
    def tearDownClass(cls) -> None:
        delete_bucket()

    def assertBucketIsEmpty(self, empty=True) -> bool:
        bucket = s3_resource.Bucket(BUCKET)  # type: ignore

        if empty:
            self.assertEqual(len(list(bucket.object_versions.all())), 0)
        else:
            self.assertNotEqual(len(list(bucket.object_versions.all())), 0)

        return True

    def test_delete_all_bad_region(self) -> None:
        r = client.post(
            objects_delete_url,
            json={
                "region": "bad_region",
                "access_key": ACCESS_KEY,
                "secret_access_key": SECRET_KEY,
                "skip_current": False,
                "skip_objects": False,
                "skip_markers": False,
                "skip_mpus": False,
                "dry_run": False,
            },
        )

        self.assertEqual(r.status_code, HTTPStatus.NOT_FOUND)

    def test_delete_all_bad_keys(self) -> None:
        """
        Tests the behavior of the delete_objects endpoint when provided with invalid access keys.

        This test case verifies that the delete_objects endpoint returns a FAILURE status
        when the provided access key is invalid.
        It sends a request to the delete_objects endpoint with an invalid access key,
        and then checks that the response status code is FAILURE.
        """

        r = client.post(
            objects_delete_url,
            json={
                "region": REGION,
                "access_key": "bad_key",
                "secret_access_key": SECRET_KEY,
                "skip_current": False,
                "skip_objects": False,
                "skip_markers": False,
                "skip_mpus": False,
                "dry_run": False,
            },
        )
        r.raise_for_status()

        self.assertEqual(r.status_code, HTTPStatus.CREATED)

        job_id = r.json()["job_id"]
        while True:
            sleep(1)
            r = client.get(f"{objects_delete_url}?job_id={job_id}")
            r.raise_for_status()
            if r.json()["state"] in ["SUCCESS", "FAILURE"]:
                break

        self.assertEqual(r.json()["state"], "FAILURE")

    def test_delete_all(self):
        """
        Deletes all objects in the S3 bucket.
        """

        for i in range(1, 10):
            s3_client.put_object(Body=b"body", Bucket=BUCKET, Key=f"file{i}.txt")

        # delete objects
        r = client.post(
            objects_delete_url,
            json={
                "region": REGION,
                "access_key": ACCESS_KEY,
                "secret_access_key": SECRET_KEY,
                "skip_current": False,
                "skip_objects": False,
                "skip_markers": False,
                "skip_mpus": False,
                "dry_run": False,
            },
        )
        r.raise_for_status()

        job_id = r.json()["job_id"]
        while True:
            sleep(1)
            r = client.get(f"{objects_delete_url}?job_id={job_id}")
            r.raise_for_status()
            if r.json()["state"] in ["SUCCESS", "FAILURE"]:
                break

        self.assertEqual(r.json()["state"], "SUCCESS")

        stats = ast.literal_eval(r.json()["status"]["message"])
        self.assertEqual(stats["stats"]["deleted objects"], 9)
        self.assertEqual(stats["stats"]["deleted total"], 9)

        # assert bucket is empty
        self.assertBucketIsEmpty()

    def test_delete_all_dry_run(self):
        """
        Deletes all objects in the S3 bucket in a dry run mode, where no actual deletions are performed.

        This test verifies the behavior of the delete objects functionality in a dry run mode,
        where the objects are not actually deleted from the S3 bucket.
        It checks that the delete operation reports the correct number of objects that would be deleted,
        without modifying the bucket contents.
        """

        for i in range(1, 10):
            s3_client.put_object(Body=b"body", Bucket=BUCKET, Key=f"file{i}.txt")

        # delete objects
        r = client.post(
            objects_delete_url,
            json={
                "region": REGION,
                "access_key": ACCESS_KEY,
                "secret_access_key": SECRET_KEY,
                "skip_current": False,
                "skip_objects": False,
                "skip_markers": False,
                "skip_mpus": False,
                "dry_run": True,
            },
        )
        r.raise_for_status()

        job_id = r.json()["job_id"]
        while True:
            sleep(1)
            r = client.get(f"{objects_delete_url}?job_id={job_id}")
            r.raise_for_status()
            if r.json()["state"] in ["SUCCESS", "FAILURE"]:
                break

        self.assertEqual(r.json()["state"], "SUCCESS")

        stats = ast.literal_eval(r.json()["status"]["message"])
        self.assertEqual(stats["stats"]["deleted objects"], 9)
        self.assertEqual(stats["stats"]["deleted total"], 9)

        # assert bucket is empty
        self.assertBucketIsEmpty(empty=False)

    def test_versioning_delete_all(self):
        """
        Deletes all objects in the S3 bucket, including any versioned objects.

        This test verifies the behavior of the delete objects functionality,
        where all objects in the S3 bucket are deleted, including any versioned objects.
        It checks that the delete operation reports the correct number of objects that were deleted,
        and that the bucket is left empty after the operation.
        """

        set_versioning()

        for i in range(1, 10):
            s3_client.put_object(Body=b"body", Bucket=BUCKET, Key=f"file{i}.txt")

        # delete objects
        r = client.post(
            objects_delete_url,
            json={
                "region": REGION,
                "access_key": ACCESS_KEY,
                "secret_access_key": SECRET_KEY,
                "skip_current": False,
                "skip_objects": False,
                "skip_markers": False,
                "skip_mpus": False,
                "dry_run": False,
            },
        )
        r.raise_for_status()

        job_id = r.json()["job_id"]
        while True:
            sleep(1)
            r = client.get(f"{objects_delete_url}?job_id={job_id}")
            r.raise_for_status()
            if r.json()["state"] in ["SUCCESS", "FAILURE"]:
                break

        self.assertEqual(r.json()["state"], "SUCCESS")

        stats = ast.literal_eval(r.json()["status"]["message"])
        self.assertEqual(stats["stats"]["deleted objects"], 9)
        self.assertEqual(stats["stats"]["deleted total"], 9)

        # assert bucket is empty
        self.assertBucketIsEmpty()

    def test_versioning_delete_all_suspended(self):
        """
        Deletes all objects in the S3 bucket, including any versioned objects when versioning is suspended.

        This test verifies the behavior of the delete objects functionality,
        where all objects in the S3 bucket are deleted, including any versioned objects,
        when versioning is suspended.
        It checks that the delete operation reports the correct number of objects that were deleted,
        and that the bucket is left empty after the operation.
        """

        set_versioning()

        for i in range(1, 10):
            s3_client.put_object(Body=b"body", Bucket=BUCKET, Key=f"file{i}.txt")

        suspend_versioning()

        # delete objects
        r = client.post(
            objects_delete_url,
            json={
                "region": REGION,
                "access_key": ACCESS_KEY,
                "secret_access_key": SECRET_KEY,
                "skip_current": False,
                "skip_objects": False,
                "skip_markers": False,
                "skip_mpus": False,
                "dry_run": False,
            },
        )
        r.raise_for_status()

        job_id = r.json()["job_id"]
        while True:
            sleep(1)
            r = client.get(f"{objects_delete_url}?job_id={job_id}")
            r.raise_for_status()
            if r.json()["state"] in ["SUCCESS", "FAILURE"]:
                break

        self.assertEqual(r.json()["state"], "SUCCESS")

        stats = ast.literal_eval(r.json()["status"]["message"])
        self.assertEqual(stats["stats"]["deleted objects"], 9)
        self.assertEqual(stats["stats"]["deleted total"], 9)

        # assert bucket is empty
        self.assertBucketIsEmpty()

    def test_versioning_delete_all_suspended2(self):
        """
        Deletes all objects in the S3 bucket, including any versioned objects when versioning is suspended.

        This test verifies the behavior of the delete objects functionality,
        where all objects in the S3 bucket are deleted, including any versioned objects,
        when versioning is suspended.
        It checks that the delete operation reports the correct number of objects that were deleted,
        and that the bucket is left empty after the operation.
        """

        set_versioning()

        for i in range(1, 10):
            s3_client.put_object(Body=b"body", Bucket=BUCKET, Key=f"file{i}.txt")

        for i in range(1, 10):
            s3_client.put_object(Body=b"body", Bucket=BUCKET, Key=f"file{i}.txt")

        suspend_versioning()

        # delete objects
        r = client.post(
            objects_delete_url,
            json={
                "region": REGION,
                "access_key": ACCESS_KEY,
                "secret_access_key": SECRET_KEY,
                "skip_current": False,
                "skip_objects": False,
                "skip_markers": False,
                "skip_mpus": False,
                "dry_run": False,
            },
        )
        r.raise_for_status()

        job_id = r.json()["job_id"]
        while True:
            sleep(1)
            r = client.get(f"{objects_delete_url}?job_id={job_id}")
            r.raise_for_status()
            if r.json()["state"] in ["SUCCESS", "FAILURE"]:
                break

        self.assertEqual(r.json()["state"], "SUCCESS")

        stats = ast.literal_eval(r.json()["status"]["message"])
        self.assertEqual(stats["stats"]["deleted objects"], 18)
        self.assertEqual(stats["stats"]["deleted total"], 18)

        # assert bucket is empty
        self.assertBucketIsEmpty()

    def test_versioning_delete_all_suspended3(self):
        """
        Deletes all objects in the S3 bucket, including any versioned objects when versioning is suspended.

        This test verifies the behavior of the delete objects functionality,
        where all objects in the S3 bucket are deleted, including any versioned objects,
        when versioning is suspended.
        It checks that the delete operation reports the correct number of deleted objects and markers,
        and that the bucket is left empty after the operation.
        """

        set_versioning()

        for i in range(1, 10):
            s3_client.put_object(Body=b"body", Bucket=BUCKET, Key=f"file{i}.txt")

        for i in range(1, 10):
            s3_client.put_object(Body=b"body", Bucket=BUCKET, Key=f"file{i}.txt")

        suspend_versioning()

        for i in range(1, 10):
            s3_client.delete_object(Bucket=BUCKET, Key=f"file{i}.txt")

        for i in range(1, 10):
            s3_client.delete_object(Bucket=BUCKET, Key=f"file{i}.txt")

        # delete objects
        r = client.post(
            objects_delete_url,
            json={
                "region": REGION,
                "access_key": ACCESS_KEY,
                "secret_access_key": SECRET_KEY,
                "skip_current": False,
                "skip_objects": False,
                "skip_markers": False,
                "skip_mpus": False,
                "dry_run": False,
            },
        )
        r.raise_for_status()

        job_id = r.json()["job_id"]
        while True:
            sleep(1)
            r = client.get(f"{objects_delete_url}?job_id={job_id}")
            r.raise_for_status()
            if r.json()["state"] in ["SUCCESS", "FAILURE"]:
                break

        self.assertEqual(r.json()["state"], "SUCCESS")

        stats = ast.literal_eval(r.json()["status"]["message"])
        self.assertEqual(stats["stats"]["deleted markers"], 9)
        self.assertEqual(stats["stats"]["deleted objects"], 18)
        self.assertEqual(stats["stats"]["deleted total"], 27)

        # assert bucket is empty
        self.assertBucketIsEmpty()

    def test_versioning_delete_all_pagesize(self):
        """
        Deletes all objects in an S3 bucket with versioning enabled, using a page size of 2 for the delete operation.
        """

        set_versioning()

        for i in range(1, 10):
            s3_client.put_object(Body=b"body", Bucket=BUCKET, Key=f"file{i}.txt")

        # delete objects
        r = client.post(
            objects_delete_url,
            json={
                "region": REGION,
                "access_key": ACCESS_KEY,
                "secret_access_key": SECRET_KEY,
                "skip_current": False,
                "skip_objects": False,
                "skip_markers": False,
                "skip_mpus": False,
                "dry_run": False,
                "page_size": 2,
            },
        )
        r.raise_for_status()

        job_id = r.json()["job_id"]
        while True:
            sleep(1)
            r = client.get(f"{objects_delete_url}?job_id={job_id}")
            r.raise_for_status()
            if r.json()["state"] in ["SUCCESS", "FAILURE"]:
                break

        self.assertEqual(r.json()["state"], "SUCCESS")

        stats = ast.literal_eval(r.json()["status"]["message"])
        self.assertEqual(stats["stats"]["deleted objects"], 9)
        self.assertEqual(stats["stats"]["deleted total"], 9)

        # assert bucket is empty
        self.assertBucketIsEmpty()

    def test_versioning_delete_all_pagesize_suspended(self):
        """
        Deletes all objects in an S3 bucket with versioning suspended, using a page size of 2 for the delete operation.
        """

        set_versioning()

        for i in range(1, 10):
            s3_client.put_object(Body=b"body", Bucket=BUCKET, Key=f"file{i}.txt")

        suspend_versioning()

        # delete objects
        r = client.post(
            objects_delete_url,
            json={
                "region": REGION,
                "access_key": ACCESS_KEY,
                "secret_access_key": SECRET_KEY,
                "skip_current": False,
                "skip_objects": False,
                "skip_markers": False,
                "skip_mpus": False,
                "dry_run": False,
                "page_size": 2,
            },
        )
        r.raise_for_status()

        job_id = r.json()["job_id"]
        while True:
            sleep(1)
            r = client.get(f"{objects_delete_url}?job_id={job_id}")
            r.raise_for_status()
            if r.json()["state"] in ["SUCCESS", "FAILURE"]:
                break

        self.assertEqual(r.json()["state"], "SUCCESS")

        stats = ast.literal_eval(r.json()["status"]["message"])
        self.assertEqual(stats["stats"]["deleted objects"], 9)
        self.assertEqual(stats["stats"]["deleted total"], 9)

        # assert bucket is empty
        self.assertBucketIsEmpty()

    def test_versioning_delete_all_pagesize_keymarker(self):
        """
        Deletes all objects in an S3 bucket with versioning enabled, using a page size of 2 for the delete operation
        and starting the delete from the object with the key "file2.txt".

        This test verifies that the delete operation can be performed in multiple pages,
        and that the keymarker parameter is used correctly
        to continue the delete from the specified object.
        """

        set_versioning()

        for i in range(1, 10):
            s3_client.put_object(Body=b"body", Bucket=BUCKET, Key=f"file{i}.txt")

        # delete objects
        r = client.post(
            objects_delete_url,
            json={
                "region": REGION,
                "access_key": ACCESS_KEY,
                "secret_access_key": SECRET_KEY,
                "skip_current": False,
                "skip_objects": False,
                "skip_markers": False,
                "skip_mpus": False,
                "dry_run": False,
                "page_size": 2,
                "keymarker": "file2.txt",
            },
        )
        r.raise_for_status()

        job_id = r.json()["job_id"]
        while True:
            sleep(1)
            r = client.get(f"{objects_delete_url}?job_id={job_id}")
            r.raise_for_status()
            if r.json()["state"] in ["SUCCESS", "FAILURE"]:
                break

        self.assertEqual(r.json()["state"], "SUCCESS")

        stats = ast.literal_eval(r.json()["status"]["message"])
        self.assertEqual(stats["stats"]["deleted objects"], 7)
        self.assertEqual(stats["stats"]["deleted total"], 7)

    def test_versioning_delete_all_dry_run(self):
        """
        Verifies that the delete operation can be performed in a dry run mode, where objects are not actually deleted
        but the operation is simulated.

        This test sets up an S3 bucket with versioning enabled and creates 9 objects.
        It then runs a delete operation in dry run mode,
        which should report the number of objects that would be deleted without actually deleting them.
        Finally, it asserts that the bucket is not empty, as the objects should not have been deleted.
        """

        set_versioning()

        for i in range(1, 10):
            s3_client.put_object(Body=b"body", Bucket=BUCKET, Key=f"file{i}.txt")

        # delete objects
        r = client.post(
            objects_delete_url,
            json={
                "region": REGION,
                "access_key": ACCESS_KEY,
                "secret_access_key": SECRET_KEY,
                "skip_current": False,
                "skip_objects": False,
                "skip_markers": False,
                "skip_mpus": False,
                "dry_run": True,
            },
        )
        r.raise_for_status()

        job_id = r.json()["job_id"]
        while True:
            sleep(1)
            r = client.get(f"{objects_delete_url}?job_id={job_id}")
            r.raise_for_status()
            if r.json()["state"] in ["SUCCESS", "FAILURE"]:
                break

        self.assertEqual(r.json()["state"], "SUCCESS")

        stats = ast.literal_eval(r.json()["status"]["message"])
        self.assertEqual(stats["stats"]["deleted objects"], 9)
        self.assertEqual(stats["stats"]["deleted total"], 9)

        # assert bucket is empty
        self.assertBucketIsEmpty(empty=False)

    def test_versioning_prefix_delete_all(self):
        """
        Performs a test to verify that the object deletion process can delete all objects in a prefix.

        This test sets up 9 objects in the "foo/" prefix and 9 objects in the "bar/" prefix of the test bucket.
        It then runs a delete operation to delete all objects in the "foo/" prefix,
        and verifies that 9 objects were deleted.
        It then runs a delete operation to delete all objects in the "bar/" prefix,
        and verifies that 9 more objects were deleted.
        Finally, it asserts that the bucket is empty.
        """

        set_versioning()

        for i in range(1, 10):
            s3_client.put_object(Body=b"body", Bucket=BUCKET, Key=f"foo/file{i}.txt")
        for i in range(1, 10):
            s3_client.put_object(Body=b"body", Bucket=BUCKET, Key=f"bar/file{i}.txt")

        # delete objects
        r = client.post(
            objects_delete_url,
            json={
                "region": REGION,
                "access_key": ACCESS_KEY,
                "secret_access_key": SECRET_KEY,
                "prefix": "foo/",
                "skip_current": False,
                "skip_objects": False,
                "skip_markers": False,
                "skip_mpus": False,
                "dry_run": False,
            },
        )
        r.raise_for_status()

        job_id = r.json()["job_id"]
        while True:
            sleep(1)
            r = client.get(f"{objects_delete_url}?job_id={job_id}")
            r.raise_for_status()
            if r.json()["state"] in ["SUCCESS", "FAILURE"]:
                break

        self.assertEqual(r.json()["state"], "SUCCESS")

        stats = ast.literal_eval(r.json()["status"]["message"])
        self.assertEqual(stats["stats"]["deleted objects"], 9)
        self.assertEqual(stats["stats"]["deleted total"], 9)

        # assert bucket is empty
        self.assertBucketIsEmpty(False)

        # delete objects
        r = client.post(
            objects_delete_url,
            json={
                "region": REGION,
                "access_key": ACCESS_KEY,
                "secret_access_key": SECRET_KEY,
                "prefix": "bar/",
                "skip_current": False,
                "skip_objects": False,
                "skip_markers": False,
                "skip_mpus": False,
                "dry_run": False,
            },
        )
        r.raise_for_status()

        job_id = r.json()["job_id"]
        while True:
            sleep(1)
            r = client.get(f"{objects_delete_url}?job_id={job_id}")
            r.raise_for_status()
            if r.json()["state"] in ["SUCCESS", "FAILURE"]:
                break

        self.assertEqual(r.json()["state"], "SUCCESS")

        stats = ast.literal_eval(r.json()["status"]["message"])
        self.assertEqual(stats["stats"]["deleted objects"], 9)
        self.assertEqual(stats["stats"]["deleted total"], 9)

        # assert bucket is empty
        self.assertBucketIsEmpty()

    def test_versioning_skip_current(self):
        """
        Performs a test to verify that the object deletion process skips deleting the current version of objects
        when the `skip_current` flag is set to `True`.

        This test case sets up 9 objects in the test bucket, then creates a new version for the last 9 objects.

        The test then attempts to delete the non-current versions of the objects,
        with the `skip_current` flag set to `True`.
        The test verifies that only the non-current versions of the objects were deleted,
        and that the bucket is not empty (the current versions remain).
        """

        set_versioning()

        for i in range(1, 10):
            s3_client.put_object(Body=b"body", Bucket=BUCKET, Key=f"file{i}.txt")

        # create new version
        for i in range(1, 10):
            s3_client.put_object(Body=b"new body", Bucket=BUCKET, Key=f"file{i}.txt")

        # delete non current objects
        r = client.post(
            objects_delete_url,
            json={
                "region": REGION,
                "access_key": ACCESS_KEY,
                "secret_access_key": SECRET_KEY,
                "skip_current": True,
                "skip_objects": False,
                "skip_markers": False,
                "skip_mpus": False,
                "dry_run": False,
            },
        )
        r.raise_for_status()

        job_id = r.json()["job_id"]
        while True:
            sleep(1)
            r = client.get(f"{objects_delete_url}?job_id={job_id}")
            r.raise_for_status()
            if r.json()["state"] in ["SUCCESS", "FAILURE"]:
                break

        self.assertEqual(r.json()["state"], "SUCCESS")

        stats = ast.literal_eval(r.json()["status"]["message"])
        self.assertEqual(stats["stats"]["deleted objects"], 9)
        self.assertEqual(stats["stats"]["deleted total"], 9)

        # assert bucket is empty
        self.assertBucketIsEmpty(empty=False)

    def test_versioning_skip_markers_01(self):
        """
        Performs a test to verify that the object deletion process skips deleting object markers
        when the `skip_markers` flag is set to `True` and the `skip_current` flag is set to `False`.

        This test case sets up 9 objects in the test bucket, then creates a new version for the last 9 objects.

        The test then attempts to delete the objects, but with the `skip_markers` flag set to `True`.
        The test verifies that that the bucket is empty (no delete markers were created)
        """

        set_versioning()

        for i in range(1, 10):
            s3_client.put_object(Body=b"body", Bucket=BUCKET, Key=f"file{i}.txt")

        for i in range(1, 10):
            s3_client.put_object(Body=b"new body", Bucket=BUCKET, Key=f"file{i}.txt")

        # delete objects
        r = client.post(
            objects_delete_url,
            json={
                "region": REGION,
                "access_key": ACCESS_KEY,
                "secret_access_key": SECRET_KEY,
                "skip_current": False,
                "skip_markers": True,
                "skip_objects": False,
                "skip_mpus": False,
                "dry_run": False,
            },
        )
        r.raise_for_status()

        job_id = r.json()["job_id"]
        while True:
            sleep(1)
            r = client.get(f"{objects_delete_url}?job_id={job_id}")
            r.raise_for_status()
            if r.json()["state"] in ["SUCCESS", "FAILURE"]:
                break

        self.assertEqual(r.json()["state"], "SUCCESS")

        stats = ast.literal_eval(r.json()["status"]["message"])
        self.assertEqual(stats["stats"]["deleted objects"], 18)
        self.assertEqual(stats["stats"]["deleted total"], 18)

        # assert bucket is empty
        self.assertBucketIsEmpty()

    def test_versioning_skip_markers_02(self):
        """
        Performs a test to verify that the object deletion process skips deleting object markers
        when the `skip_markers` flag is set to `True` and the `skip_current` flag is set to `True`.

        This test case sets up 9 objects in the test bucket, then creates a new version for the last 9 objects.
        It then deletes the latest version of the last 9 objects, leaving behind delete markers.

        The test then attempts to delete the objects, but with the `skip_markers` flag set to `True`
        and the `skip_current` flag set to `True`.
        The test verifies that the object markers were not deleted, and that the bucket is not empty.
        """

        set_versioning()

        for i in range(1, 10):
            s3_client.put_object(Body=b"body", Bucket=BUCKET, Key=f"file{i}.txt")

        for i in range(1, 10):
            s3_client.put_object(Body=b"new body", Bucket=BUCKET, Key=f"file{i}.txt")

        # delete objects
        r = client.post(
            objects_delete_url,
            json={
                "region": REGION,
                "access_key": ACCESS_KEY,
                "secret_access_key": SECRET_KEY,
                "skip_current": True,
                "skip_objects": False,
                "skip_markers": False,
                "skip_mpus": False,
                "dry_run": False,
            },
        )
        r.raise_for_status()

        job_id = r.json()["job_id"]
        while True:
            sleep(1)
            r = client.get(f"{objects_delete_url}?job_id={job_id}")
            r.raise_for_status()
            if r.json()["state"] in ["SUCCESS", "FAILURE"]:
                break

        self.assertEqual(r.json()["state"], "SUCCESS")

        stats = ast.literal_eval(r.json()["status"]["message"])
        self.assertEqual(stats["stats"]["deleted objects"], 9)
        self.assertEqual(stats["stats"]["deleted total"], 9)

        r = client.post(
            objects_delete_url,
            json={
                "region": REGION,
                "access_key": ACCESS_KEY,
                "secret_access_key": SECRET_KEY,
                "skip_current": True,
                "skip_markers": True,
                "skip_objects": False,
                "skip_mpus": False,
                "dry_run": False,
            },
        )
        r.raise_for_status()

        job_id = r.json()["job_id"]
        while True:
            sleep(1)
            r = client.get(f"{objects_delete_url}?job_id={job_id}")
            r.raise_for_status()
            if r.json()["state"] in ["SUCCESS", "FAILURE"]:
                break

        self.assertEqual(r.json()["state"], "SUCCESS")

        stats = ast.literal_eval(r.json()["status"]["message"])
        self.assertEqual(stats["stats"]["deleted objects"], 0)
        self.assertEqual(stats["stats"]["deleted total"], 0)

        # assert bucket is empty
        self.assertBucketIsEmpty(False)

    def test_versioning_skip_markers_03(self):
        """
        Performs a test to verify that the object deletion process skips deleting object markers
        when the `skip_markers` flag is set to `False` and the `orphans_only` flag is set to `True`.

        This test case sets up 9 objects in the test bucket, then creates a new version for the last 5 objects.
        It then deletes the latest version of the last 5 objects, leaving behind delete markers.

        The test then attempts to delete the objects,
        but with the `skip_markers` flag set to `False` and the `orphans_only` flag set to `True`.
        The test verifies that the object markers were deleted, and that the bucket is still not empty.
        """

        set_versioning()

        for i in range(1, 10):
            s3_client.put_object(Body=b"body", Bucket=BUCKET, Key=f"file{i}.txt")

        for i in range(1, 10):
            s3_client.delete_object(Bucket=BUCKET, Key=f"file{i}.txt")

        for i in range(5, 10):
            version = list(
                s3_client.list_object_versions(
                    Bucket=BUCKET, Prefix=f"file{i}.txt"
                ).get("Versions", [])
            )

            s3_client.delete_objects(
                Bucket=BUCKET,
                Delete={
                    "Objects": [
                        {"Key": f"file{i}.txt", "VersionId": version[0]["VersionId"]}
                    ]
                },
            )

        # delete objects
        r = client.post(
            objects_delete_url,
            json={
                "region": REGION,
                "access_key": ACCESS_KEY,
                "secret_access_key": SECRET_KEY,
                "skip_current": False,
                "skip_objects": True,
                "skip_markers": False,
                "orphans_only": True,
                "skip_mpus": False,
                "dry_run": False,
            },
        )
        r.raise_for_status()

        job_id = r.json()["job_id"]
        while True:
            sleep(1)
            r = client.get(f"{objects_delete_url}?job_id={job_id}")
            r.raise_for_status()
            if r.json()["state"] in ["SUCCESS", "FAILURE"]:
                break

        self.assertEqual(r.json()["state"], "SUCCESS")

        stats = ast.literal_eval(r.json()["status"]["message"])

        self.assertEqual(stats["stats"]["deleted markers"], 5)
        self.assertEqual(stats["stats"]["skipped markers"], 4)
        self.assertEqual(stats["stats"]["deleted total"], 5)
        self.assertEqual(stats["stats"]["skipped total"], 8)

        # delete objects
        r = client.post(
            objects_delete_url,
            json={
                "region": REGION,
                "access_key": ACCESS_KEY,
                "secret_access_key": SECRET_KEY,
                "skip_current": False,
                "skip_objects": True,
                "skip_markers": False,
                "orphans_only": False,
                "skip_mpus": False,
                "dry_run": False,
            },
        )
        r.raise_for_status()

        job_id = r.json()["job_id"]
        while True:
            sleep(1)
            r = client.get(f"{objects_delete_url}?job_id={job_id}")
            r.raise_for_status()
            if r.json()["state"] in ["SUCCESS", "FAILURE"]:
                break

        self.assertEqual(r.json()["state"], "SUCCESS")

        stats = ast.literal_eval(r.json()["status"]["message"])
        self.assertEqual(stats["stats"]["deleted markers"], 4)
        self.assertEqual(stats["stats"]["skipped markers"], 0)
        self.assertEqual(stats["stats"]["deleted total"], 4)
        self.assertEqual(stats["stats"]["skipped total"], 4)

        # assert bucket is empty
        self.assertBucketIsEmpty(False)

    def test_versioning_skip_markers_04(self):
        """
        Performs a test to verify that the object deletion process skips deleting object markers
        when the `skip_markers` flag is set to `True` and the `orphans_only` flag is set to `True`.

        This test case sets up 9 objects in the test bucket, then creates a new version for the last 5 objects.
        It then deletes the latest version of the last 5 objects, leaving behind object markers.

        The test then attempts to delete the objects,
        but with the `skip_markers` flag set to `True` and the `orphans_only` flag set to `True`.
        The test verifies that no object markers were actually deleted, and that the bucket is still not empty.
        """

        set_versioning()

        for i in range(1, 10):
            s3_client.put_object(Body=b"body", Bucket=BUCKET, Key=f"file{i}.txt")

        for i in range(1, 10):
            s3_client.delete_object(Bucket=BUCKET, Key=f"file{i}.txt")

        for i in range(5, 10):
            version = list(
                s3_client.list_object_versions(
                    Bucket=BUCKET, Prefix=f"file{i}.txt"
                ).get("Versions", [])
            )

            s3_client.delete_objects(
                Bucket=BUCKET,
                Delete={
                    "Objects": [
                        {"Key": f"file{i}.txt", "VersionId": version[0]["VersionId"]}
                    ]
                },
            )

        # delete objects
        r = client.post(
            objects_delete_url,
            json={
                "region": REGION,
                "access_key": ACCESS_KEY,
                "secret_access_key": SECRET_KEY,
                "skip_current": False,
                "skip_objects": False,
                "skip_markers": True,
                "orphans_only": True,
                "skip_mpus": False,
                "dry_run": False,
            },
        )
        r.raise_for_status()

        job_id = r.json()["job_id"]
        while True:
            sleep(1)
            r = client.get(f"{objects_delete_url}?job_id={job_id}")
            r.raise_for_status()
            if r.json()["state"] in ["SUCCESS", "FAILURE"]:
                break

        self.assertEqual(r.json()["state"], "SUCCESS")

        stats = ast.literal_eval(r.json()["status"]["message"])

        self.assertEqual(stats["stats"]["deleted markers"], 0)
        self.assertEqual(stats["stats"]["skipped markers"], 9)
        self.assertEqual(stats["stats"]["deleted total"], 4)
        self.assertEqual(stats["stats"]["skipped total"], 9)

    def test_versioning_skip_markers_05(self):
        set_versioning()

        for i in range(1, 10):
            s3_client.put_object(Body=b"body", Bucket=BUCKET, Key=f"file{i}.txt")

        for i in range(1, 10):
            s3_client.delete_object(Bucket=BUCKET, Key=f"file{i}.txt")

        for i in range(1, 10):
            s3_client.put_object(Body=b"body", Bucket=BUCKET, Key=f"file{i}.txt")

        # delete objects
        r = client.post(
            objects_delete_url,
            json={
                "region": REGION,
                "access_key": ACCESS_KEY,
                "secret_access_key": SECRET_KEY,
                "skip_current": True,
                "skip_objects": True,
                "skip_markers": False,
                "orphans_only": False,
                "skip_mpus": True,
                "dry_run": False,
            },
        )
        r.raise_for_status()

        job_id = r.json()["job_id"]
        while True:
            sleep(1)
            r = client.get(f"{objects_delete_url}?job_id={job_id}")
            r.raise_for_status()
            if r.json()["state"] in ["SUCCESS", "FAILURE"]:
                break

        self.assertEqual(r.json()["state"], "SUCCESS")

        stats = ast.literal_eval(r.json()["status"]["message"])

        self.assertEqual(stats["stats"]["deleted markers"], 0)
        self.assertEqual(stats["stats"]["skipped markers"], 9)
        self.assertEqual(stats["stats"]["deleted total"], 0)
        self.assertEqual(stats["stats"]["skipped total"], 27)

    def test_versioning_skip_objects(self):
        """
        Performs a test to verify that the object deletion process skips deleting objects
        when the `skip_objects` flag is set to `True`.

        This test case sets up 9 objects in the test bucket, then creates a new version for each object.
        It then attempts to delete the objects, but with the `skip_objects` flag set to `True`.
        The test verifies that no objects were actually deleted, and that the bucket is still not empty.
        """

        set_versioning()

        for i in range(1, 10):
            s3_client.put_object(Body=b"body", Bucket=BUCKET, Key=f"file{i}.txt")

        for i in range(1, 10):
            s3_client.put_object(Body=b"new body", Bucket=BUCKET, Key=f"file{i}.txt")

        # delete objects
        r = client.post(
            objects_delete_url,
            json={
                "region": REGION,
                "access_key": ACCESS_KEY,
                "secret_access_key": SECRET_KEY,
                "skip_current": False,
                "skip_objects": True,
                "skip_markers": False,
                "skip_mpus": False,
                "dry_run": False,
            },
        )
        r.raise_for_status()

        job_id = r.json()["job_id"]
        while True:
            sleep(1)
            r = client.get(f"{objects_delete_url}?job_id={job_id}")
            r.raise_for_status()
            if r.json()["state"] in ["SUCCESS", "FAILURE"]:
                break

        self.assertEqual(r.json()["state"], "SUCCESS")

        stats = ast.literal_eval(r.json()["status"]["message"])
        self.assertEqual(stats["stats"]["deleted objects"], 0)
        self.assertEqual(stats["stats"]["deleted total"], 0)

        # assert bucket is empty
        self.assertBucketIsEmpty(False)

    def test_delete_mpus(self):
        """
        Performs a test to delete multipart uploads (MPUs) from an S3 bucket without actually deleting the objects.

        This test case sets up 9 objects in the test bucket,
        then attempts to delete the MPUs without actually deleting the objects.
        It verifies that the delete operation reports success, but that no objects were actually deleted.
        """

        for i in range(1, 10):
            s3_client.put_object(Body=b"body", Bucket=BUCKET, Key=f"file{i}.txt")

        # delete objects
        r = client.post(
            objects_delete_url,
            json={
                "region": REGION,
                "access_key": ACCESS_KEY,
                "secret_access_key": SECRET_KEY,
                "skip_current": False,
                "skip_markers": False,
                "skip_objects": True,
                "skip_mpus": False,
                "dry_run": False,
            },
        )
        r.raise_for_status()

        job_id = r.json()["job_id"]
        while True:
            sleep(1)
            r = client.get(f"{objects_delete_url}?job_id={job_id}")
            r.raise_for_status()
            if r.json()["state"] in ["SUCCESS", "FAILURE"]:
                break

        self.assertEqual(r.json()["state"], "SUCCESS")

        stats = ast.literal_eval(r.json()["status"]["message"])
        self.assertEqual(stats["stats"]["deleted objects"], 0)
        self.assertEqual(stats["stats"]["deleted total"], 0)

        # assert bucket is empty
        self.assertBucketIsEmpty(empty=False)

    def test_delete_mpus_dry_run(self):
        """
        Performs a dry run of deleting multipart uploads (MPUs) from an S3 bucket.

        This test case sets up 9 objects in the test bucket, then attempts to delete the
        MPUs without actually deleting the objects. It verifies that the delete operation
        reports success, but that no objects were actually deleted.
        """

        for i in range(1, 10):
            s3_client.put_object(Body=b"body", Bucket=BUCKET, Key=f"file{i}.txt")

        # delete objects
        r = client.post(
            objects_delete_url,
            json={
                "region": REGION,
                "access_key": ACCESS_KEY,
                "secret_access_key": SECRET_KEY,
                "skip_objects": True,
                "skip_mpus": False,
                "dry_run": True,
                "skip_markers": False,
                "skip_current": False,
            },
        )
        r.raise_for_status()

        job_id = r.json()["job_id"]
        while True:
            sleep(1)
            r = client.get(f"{objects_delete_url}?job_id={job_id}")
            r.raise_for_status()
            if r.json()["state"] in ["SUCCESS", "FAILURE"]:
                break

        self.assertEqual(r.json()["state"], "SUCCESS")

        stats = ast.literal_eval(r.json()["status"]["message"])
        self.assertEqual(stats["stats"]["deleted objects"], 0)
        self.assertEqual(stats["stats"]["deleted total"], 0)

        # assert bucket is empty
        self.assertBucketIsEmpty(empty=False)


if __name__ == "__main__":
    unittest.main()

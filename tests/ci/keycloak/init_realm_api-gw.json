{"id": "api-gw", "realm": "api-gw", "notBefore": 0, "defaultSignatureAlgorithm": "RS256", "revokeRefreshToken": false, "refreshTokenMaxReuse": 0, "accessTokenLifespan": 300, "accessTokenLifespanForImplicitFlow": 900, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "ssoSessionIdleTimeoutRememberMe": 0, "ssoSessionMaxLifespanRememberMe": 0, "offlineSessionIdleTimeout": 2592000, "offlineSessionMaxLifespanEnabled": false, "offlineSessionMaxLifespan": 5184000, "clientSessionIdleTimeout": 0, "clientSessionMaxLifespan": 0, "clientOfflineSessionIdleTimeout": 0, "clientOfflineSessionMaxLifespan": 0, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "oauth2DeviceCodeLifespan": 600, "oauth2DevicePollingInterval": 5, "enabled": true, "sslRequired": "external", "registrationAllowed": false, "registrationEmailAsUsername": false, "rememberMe": false, "verifyEmail": false, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": false, "editUsernameAllowed": false, "bruteForceProtected": false, "permanentLockout": false, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "roles": {"realm": [{"id": "5412435d-5b5b-4f08-a6c0-7ad825e8b4ee", "name": "jobs_api_read", "composite": false, "clientRole": false, "containerId": "api-gw", "attributes": {}}, {"id": "2844c013-445c-4e40-952c-149e2a0faf8a", "name": "data_api_read", "composite": false, "clientRole": false, "containerId": "api-gw", "attributes": {}}, {"id": "53f7149e-7450-4fb1-a04d-779b8703d150", "name": "offline_access", "description": "${role_offline-access}", "composite": false, "clientRole": false, "containerId": "api-gw", "attributes": {}}, {"id": "4e9b48d6-cab7-4a2e-8e7f-6eb5cfaf07d9", "name": "demo_api_write", "composite": false, "clientRole": false, "containerId": "api-gw", "attributes": {}}, {"id": "e8a4b3bc-14c9-47b5-87d7-d6be423a404b", "name": "jobs_api_write", "composite": false, "clientRole": false, "containerId": "api-gw", "attributes": {}}, {"id": "03a95a7c-38bd-4ded-ab4b-95bfc755b7b5", "name": "default-roles-api-gw", "description": "${role_default-roles}", "composite": true, "composites": {"realm": ["offline_access", "uma_authorization"], "client": {"account": ["view-profile", "manage-account"]}}, "clientRole": false, "containerId": "api-gw", "attributes": {}}, {"id": "********-ab12-4a99-9169-901a415f86b1", "name": "uma_authorization", "description": "${role_uma_authorization}", "composite": false, "clientRole": false, "containerId": "api-gw", "attributes": {}}, {"id": "c2474ec9-89f6-4532-a21f-5337148c328f", "name": "data_api_write", "composite": false, "clientRole": false, "containerId": "api-gw", "attributes": {}}, {"id": "431de656-8426-42c4-94c6-ed647527fcb5", "name": "demo_api_read", "composite": false, "clientRole": false, "containerId": "api-gw", "attributes": {}}], "client": {"realm-management": [{"id": "9526e9a5-fc58-45e9-8549-642469c8285d", "name": "manage-authorization", "description": "${role_manage-authorization}", "composite": false, "clientRole": true, "containerId": "69fa575a-c769-461d-99f7-ea9953cffc6f", "attributes": {}}, {"id": "60adcaef-c691-42e7-b501-46ab9bf2127a", "name": "query-groups", "description": "${role_query-groups}", "composite": false, "clientRole": true, "containerId": "69fa575a-c769-461d-99f7-ea9953cffc6f", "attributes": {}}, {"id": "0b27b242-75bb-4d28-b6ac-8109ba2920b6", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "composite": false, "clientRole": true, "containerId": "69fa575a-c769-461d-99f7-ea9953cffc6f", "attributes": {}}, {"id": "9d0b5136-0beb-4978-95d2-0183d1a940dd", "name": "query-users", "description": "${role_query-users}", "composite": false, "clientRole": true, "containerId": "69fa575a-c769-461d-99f7-ea9953cffc6f", "attributes": {}}, {"id": "dd7c3ef3-ef5f-4dbd-9196-3fcfb5a993d0", "name": "manage-users", "description": "${role_manage-users}", "composite": false, "clientRole": true, "containerId": "69fa575a-c769-461d-99f7-ea9953cffc6f", "attributes": {}}, {"id": "71b0fb8d-96b2-4a1d-81a1-f4fa73c21b86", "name": "manage-realm", "description": "${role_manage-realm}", "composite": false, "clientRole": true, "containerId": "69fa575a-c769-461d-99f7-ea9953cffc6f", "attributes": {}}, {"id": "d0c45967-8261-4232-9981-fb252dbe987d", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "composite": false, "clientRole": true, "containerId": "69fa575a-c769-461d-99f7-ea9953cffc6f", "attributes": {}}, {"id": "e186aeb8-7242-4c05-a34b-8ee268da255b", "name": "impersonation", "description": "${role_impersonation}", "composite": false, "clientRole": true, "containerId": "69fa575a-c769-461d-99f7-ea9953cffc6f", "attributes": {}}, {"id": "9d61c4ac-7d63-4b2f-a00a-7e81c1c869d7", "name": "query-realms", "description": "${role_query-realms}", "composite": false, "clientRole": true, "containerId": "69fa575a-c769-461d-99f7-ea9953cffc6f", "attributes": {}}, {"id": "39e4d691-b6a4-4b24-8014-a88f8e099e9b", "name": "manage-events", "description": "${role_manage-events}", "composite": false, "clientRole": true, "containerId": "69fa575a-c769-461d-99f7-ea9953cffc6f", "attributes": {}}, {"id": "8662d5f2-7f60-4102-8e39-a75310750e9b", "name": "query-clients", "description": "${role_query-clients}", "composite": false, "clientRole": true, "containerId": "69fa575a-c769-461d-99f7-ea9953cffc6f", "attributes": {}}, {"id": "ebf0e095-3e79-4b06-98b9-98f331a535d9", "name": "view-realm", "description": "${role_view-realm}", "composite": false, "clientRole": true, "containerId": "69fa575a-c769-461d-99f7-ea9953cffc6f", "attributes": {}}, {"id": "eac5d0d9-76b8-4b0f-acb4-e563811f10ff", "name": "view-authorization", "description": "${role_view-authorization}", "composite": false, "clientRole": true, "containerId": "69fa575a-c769-461d-99f7-ea9953cffc6f", "attributes": {}}, {"id": "f3876d4d-2b8b-4589-9fb6-30f236259b97", "name": "view-events", "description": "${role_view-events}", "composite": false, "clientRole": true, "containerId": "69fa575a-c769-461d-99f7-ea9953cffc6f", "attributes": {}}, {"id": "e88ecc2e-9c6d-4495-8863-aa118282e9d4", "name": "create-client", "description": "${role_create-client}", "composite": false, "clientRole": true, "containerId": "69fa575a-c769-461d-99f7-ea9953cffc6f", "attributes": {}}, {"id": "9d03e6c4-a312-4717-a20d-b2b3f7fbd639", "name": "manage-clients", "description": "${role_manage-clients}", "composite": false, "clientRole": true, "containerId": "69fa575a-c769-461d-99f7-ea9953cffc6f", "attributes": {}}, {"id": "a80ba27a-d283-42e9-8c97-198af3a515f5", "name": "view-clients", "description": "${role_view-clients}", "composite": true, "composites": {"client": {"realm-management": ["query-clients"]}}, "clientRole": true, "containerId": "69fa575a-c769-461d-99f7-ea9953cffc6f", "attributes": {}}, {"id": "72b55328-250d-4dae-a32d-b192c7db2188", "name": "realm-admin", "description": "${role_realm-admin}", "composite": true, "composites": {"client": {"realm-management": ["manage-authorization", "query-groups", "manage-identity-providers", "query-users", "manage-users", "view-identity-providers", "manage-realm", "impersonation", "query-realms", "manage-events", "query-clients", "view-realm", "view-events", "view-authorization", "create-client", "manage-clients", "view-clients", "view-users"]}}, "clientRole": true, "containerId": "69fa575a-c769-461d-99f7-ea9953cffc6f", "attributes": {}}, {"id": "********-9024-4c24-a18f-8dc04c9c2170", "name": "view-users", "description": "${role_view-users}", "composite": true, "composites": {"client": {"realm-management": ["query-groups", "query-users"]}}, "clientRole": true, "containerId": "69fa575a-c769-461d-99f7-ea9953cffc6f", "attributes": {}}], "security-admin-console": [], "internal_api": [], "admin-cli": [], "account-console": [], "broker": [{"id": "b19964dc-4ae6-4f84-bfcd-e88703cc660b", "name": "read-token", "description": "${role_read-token}", "composite": false, "clientRole": true, "containerId": "63e33014-6593-46d0-a7cd-4d20401f163b", "attributes": {}}], "account": [{"id": "c22ab2a1-43f6-4cc4-866a-2fe1be169ee9", "name": "delete-account", "description": "${role_delete-account}", "composite": false, "clientRole": true, "containerId": "6a33bd79-bde3-48b6-b152-cc4a185ab8b2", "attributes": {}}, {"id": "7c0d0c53-81db-4f91-80da-76868068a9f7", "name": "view-profile", "description": "${role_view-profile}", "composite": false, "clientRole": true, "containerId": "6a33bd79-bde3-48b6-b152-cc4a185ab8b2", "attributes": {}}, {"id": "19c306f9-4dff-431f-8d7a-15133335b479", "name": "view-applications", "description": "${role_view-applications}", "composite": false, "clientRole": true, "containerId": "6a33bd79-bde3-48b6-b152-cc4a185ab8b2", "attributes": {}}, {"id": "d80d8b22-cd55-41e2-a4ed-aacdd2f65f2c", "name": "manage-account", "description": "${role_manage-account}", "composite": true, "composites": {"client": {"account": ["manage-account-links"]}}, "clientRole": true, "containerId": "6a33bd79-bde3-48b6-b152-cc4a185ab8b2", "attributes": {}}, {"id": "8e254a39-8049-4fb9-a3a2-6fc1b366557a", "name": "view-consent", "description": "${role_view-consent}", "composite": false, "clientRole": true, "containerId": "6a33bd79-bde3-48b6-b152-cc4a185ab8b2", "attributes": {}}, {"id": "73eadf25-ca33-4e1e-b776-a934b02aa689", "name": "manage-account-links", "description": "${role_manage-account-links}", "composite": false, "clientRole": true, "containerId": "6a33bd79-bde3-48b6-b152-cc4a185ab8b2", "attributes": {}}, {"id": "d30ba768-46fa-4dab-86e2-02056e6156f8", "name": "manage-consent", "description": "${role_manage-consent}", "composite": true, "composites": {"client": {"account": ["view-consent"]}}, "clientRole": true, "containerId": "6a33bd79-bde3-48b6-b152-cc4a185ab8b2", "attributes": {}}], "demo_client": []}}, "groups": [], "defaultRole": {"id": "03a95a7c-38bd-4ded-ab4b-95bfc755b7b5", "name": "default-roles-api-gw", "description": "${role_default-roles}", "composite": true, "clientRole": false, "containerId": "api-gw"}, "requiredCredentials": ["password"], "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "otpSupportedApplications": ["FreeOTP", "Google Authenticator"], "webAuthnPolicyRpEntityName": "keycloak", "webAuthnPolicySignatureAlgorithms": ["ES256"], "webAuthnPolicyRpId": "", "webAuthnPolicyAttestationConveyancePreference": "not specified", "webAuthnPolicyAuthenticatorAttachment": "not specified", "webAuthnPolicyRequireResidentKey": "not specified", "webAuthnPolicyUserVerificationRequirement": "not specified", "webAuthnPolicyCreateTimeout": 0, "webAuthnPolicyAvoidSameAuthenticatorRegister": false, "webAuthnPolicyAcceptableAaguids": [], "webAuthnPolicyPasswordlessRpEntityName": "keycloak", "webAuthnPolicyPasswordlessSignatureAlgorithms": ["ES256"], "webAuthnPolicyPasswordlessRpId": "", "webAuthnPolicyPasswordlessAttestationConveyancePreference": "not specified", "webAuthnPolicyPasswordlessAuthenticatorAttachment": "not specified", "webAuthnPolicyPasswordlessRequireResidentKey": "not specified", "webAuthnPolicyPasswordlessUserVerificationRequirement": "not specified", "webAuthnPolicyPasswordlessCreateTimeout": 0, "webAuthnPolicyPasswordlessAvoidSameAuthenticatorRegister": false, "webAuthnPolicyPasswordlessAcceptableAaguids": [], "users": [{"id": "4eebb2ab-d2f2-4a6a-a582-29b1a626e257", "createdTimestamp": *************, "username": "service-account-demo_client", "enabled": true, "totp": false, "emailVerified": false, "serviceAccountClientId": "demo_client", "credentials": [], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["jobs_api_read", "demo_api_write", "demo_api_read"], "notBefore": 0, "groups": []}, {"id": "cfced180-172f-4930-81ca-5b932ae8f2d7", "createdTimestamp": *************, "username": "service-account-internal_api", "enabled": true, "totp": false, "emailVerified": false, "serviceAccountClientId": "internal_api", "credentials": [], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["jobs_api_read", "data_api_read", "jobs_api_write", "data_api_write"], "notBefore": 0, "groups": []}], "scopeMappings": [{"clientScope": "internal_api_scope", "roles": ["data_api_read", "jobs_api_write", "jobs_api_read", "data_api_write"]}, {"clientScope": "offline_access", "roles": ["offline_access"]}, {"clientScope": "demo_client_scope", "roles": ["jobs_api_read", "demo_api_read", "demo_api_write"]}], "clientScopeMappings": {"account": [{"client": "account-console", "roles": ["manage-account"]}]}, "clients": [{"id": "6a33bd79-bde3-48b6-b152-cc4a185ab8b2", "clientId": "account", "name": "${client_account}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/api-gw/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/realms/api-gw/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "a4a77928-8150-4f4e-a5e9-ba0190ebb53f", "clientId": "account-console", "name": "${client_account-console}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/api-gw/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/realms/api-gw/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "b9a2bd1f-f037-4d4c-9d6a-ff0f3a013cd3", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}], "defaultClientScopes": ["web-origins", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "413171c5-d455-461a-a1dc-fc38b7327210", "clientId": "admin-cli", "name": "${client_admin-cli}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "63e33014-6593-46d0-a7cd-4d20401f163b", "clientId": "broker", "name": "${client_broker}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "8b7d8edd-1de7-4950-8b3a-eaf213824551", "clientId": "demo_client", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "TIL2rr1ysrlSgh0PsnLheJ3MtLSCeUZf", "redirectUris": ["/"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": true, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"saml.multivalued.roles": "false", "saml.force.post.binding": "false", "oauth2.device.authorization.grant.enabled": "false", "backchannel.logout.revoke.offline.tokens": "false", "saml.server.signature.keyinfo.ext": "false", "use.refresh.tokens": "true", "oidc.ciba.grant.enabled": "false", "backchannel.logout.session.required": "true", "client_credentials.use_refresh_token": "false", "saml.client.signature": "false", "require.pushed.authorization.requests": "false", "saml.assertion.signature": "false", "id.token.as.detached.signature": "false", "saml.encrypt": "false", "saml.server.signature": "false", "exclude.session.state.from.auth.response": "false", "saml.artifact.binding": "false", "saml_force_name_id_format": "false", "tls.client.certificate.bound.access.tokens": "false", "acr.loa.map": "{}", "saml.authnstatement": "false", "display.on.consent.screen": "false", "token.response.type.bearer.lower-case": "false", "saml.onetimeuse.condition": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"id": "fdebb0b1-5904-4a32-981c-2f9c61bd149b", "name": "Client Host", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientHost", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientHost", "jsonType.label": "String"}}, {"id": "360595a4-dd3f-4cd1-b797-29ed1eafa3fb", "name": "Client ID", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientId", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientId", "jsonType.label": "String"}}, {"id": "0ec4b519-b9a8-427f-a8ac-c78161898921", "name": "Client IP Address", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientAddress", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientAddress", "jsonType.label": "String"}}], "defaultClientScopes": ["demo_client_scope"], "optionalClientScopes": []}, {"id": "1f144f13-1e58-478c-a4fd-18e81e8defef", "clientId": "internal_api", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "p8Om4VEioeq5xTRNRI2DZTtTxTKUB6Kv", "redirectUris": ["/"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": true, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"saml.multivalued.roles": "false", "saml.force.post.binding": "false", "oauth2.device.authorization.grant.enabled": "false", "backchannel.logout.revoke.offline.tokens": "false", "saml.server.signature.keyinfo.ext": "false", "use.refresh.tokens": "true", "oidc.ciba.grant.enabled": "false", "backchannel.logout.session.required": "true", "client_credentials.use_refresh_token": "false", "saml.client.signature": "false", "require.pushed.authorization.requests": "false", "saml.assertion.signature": "false", "id.token.as.detached.signature": "false", "saml.encrypt": "false", "saml.server.signature": "false", "exclude.session.state.from.auth.response": "false", "saml.artifact.binding": "false", "saml_force_name_id_format": "false", "tls.client.certificate.bound.access.tokens": "false", "acr.loa.map": "{}", "saml.authnstatement": "false", "display.on.consent.screen": "false", "token.response.type.bearer.lower-case": "false", "saml.onetimeuse.condition": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"id": "0d841b01-b1a6-42e4-bfb6-2f7792e7c588", "name": "Client Host", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientHost", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientHost", "jsonType.label": "String"}}, {"id": "d0cb9162-59bd-422d-93fc-048bd7753323", "name": "Client ID", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientId", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientId", "jsonType.label": "String"}}, {"id": "1007419a-e967-4acc-8c55-75031c3e711c", "name": "Client IP Address", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientAddress", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientAddress", "jsonType.label": "String"}}], "defaultClientScopes": ["internal_api_scope"], "optionalClientScopes": []}, {"id": "69fa575a-c769-461d-99f7-ea9953cffc6f", "clientId": "realm-management", "name": "${client_realm-management}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "********-051d-4f0e-997c-4472bda69ee2", "clientId": "security-admin-console", "name": "${client_security-admin-console}", "rootUrl": "${authAdminUrl}", "baseUrl": "/admin/api-gw/console/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/admin/api-gw/console/*"], "webOrigins": ["+"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "153d782d-98be-47ae-a502-1386658e7acc", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}], "clientScopes": [{"id": "6703755a-6f02-4c49-904a-cabc60302a86", "name": "microprofile-jwt", "description": "Microprofile - JWT built-in scope", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "6e35e302-8331-4384-b6da-9582a1408077", "name": "groups", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"multivalued": "true", "user.attribute": "foo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "groups", "jsonType.label": "String"}}, {"id": "64cd8c8c-2061-4fdf-8abc-6ec97f8113b5", "name": "upn", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "upn", "jsonType.label": "String"}}]}, {"id": "7e2016e6-4b8b-44ec-b63e-7d05f331844f", "name": "role_list", "description": "SAML role list", "protocol": "saml", "attributes": {"consent.screen.text": "${samlRoleListScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "041fba5b-5296-44df-b32a-6376b56e882b", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}]}, {"id": "35005ded-833c-4971-8219-3a78545d3586", "name": "email", "description": "OpenID Connect built-in scope: email", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${emailScopeConsentText}"}, "protocolMappers": [{"id": "672b2ae0-dbeb-4f37-a52f-0adc04d947de", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "225158bd-8381-4310-887a-cd39b0ae17db", "name": "email verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "emailVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email_verified", "jsonType.label": "boolean"}}]}, {"id": "09a33f2a-3605-4b7e-9732-690fdd074ef4", "name": "profile", "description": "OpenID Connect built-in scope: profile", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${profileScopeConsentText}"}, "protocolMappers": [{"id": "c978ed93-a450-42b6-b76f-63e727583e98", "name": "updated at", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "updatedAt", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "updated_at", "jsonType.label": "String"}}, {"id": "32688de4-24f5-4788-95b9-f70e28496088", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}, {"id": "c93d424f-09f2-4c61-97c2-f4e88981ad29", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "access.token.claim": "true", "userinfo.token.claim": "true"}}, {"id": "3aaac2d7-ea1b-467e-84ed-918ce703cb76", "name": "picture", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "picture", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "picture", "jsonType.label": "String"}}, {"id": "820039cb-f903-40ae-a8ec-822d05aff184", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "ad68eea8-3d74-4230-b1d9-a933de9fd652", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "9c0e23d4-2694-411d-b901-8928a9cb82e4", "name": "birthdate", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "birthdate", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "birthdate", "jsonType.label": "String"}}, {"id": "3b1709ef-a194-462f-a920-683feb1e82d0", "name": "middle name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "middleName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "middle_name", "jsonType.label": "String"}}, {"id": "2ce80347-9b32-4047-82fe-1934c5baffec", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "9a0a6ebd-f654-420f-a08b-d97ff85339fa", "name": "profile", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "profile", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "profile", "jsonType.label": "String"}}, {"id": "0acdd658-6c73-4bd1-86aa-467e819c7c7a", "name": "website", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "website", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "website", "jsonType.label": "String"}}, {"id": "fb90bf21-2531-49f8-a465-f16dc74048d9", "name": "zoneinfo", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "zoneinfo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "zoneinfo", "jsonType.label": "String"}}, {"id": "0ac63545-64c4-4b74-9118-297565a255be", "name": "nickname", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "nickname", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "nickname", "jsonType.label": "String"}}, {"id": "37f2756d-f19b-4d21-b7e5-3cee70cd1883", "name": "gender", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "gender", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "gender", "jsonType.label": "String"}}]}, {"id": "99eab500-6488-4557-9818-4b7253edd04f", "name": "address", "description": "OpenID Connect built-in scope: address", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${addressScopeConsentText}"}, "protocolMappers": [{"id": "d60f6ed9-a320-4a46-81ab-dd8bab2c3e9d", "name": "address", "protocol": "openid-connect", "protocolMapper": "oidc-address-mapper", "consentRequired": false, "config": {"user.attribute.formatted": "formatted", "user.attribute.country": "country", "user.attribute.postal_code": "postal_code", "userinfo.token.claim": "true", "user.attribute.street": "street", "id.token.claim": "true", "user.attribute.region": "region", "access.token.claim": "true", "user.attribute.locality": "locality"}}]}, {"id": "717b0a0b-9ec0-4000-a679-b81c2881cead", "name": "phone", "description": "OpenID Connect built-in scope: phone", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${phoneScopeConsentText}"}, "protocolMappers": [{"id": "626250cb-2bb5-4117-8926-7947da4f4143", "name": "phone number verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "phoneNumberVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number_verified", "jsonType.label": "boolean"}}, {"id": "6da37f31-61d4-46bc-ac38-fe2d1681ae14", "name": "phone number", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "phoneNumber", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number", "jsonType.label": "String"}}]}, {"id": "74ae348f-a01a-44f8-8135-24d6c7b91ef7", "name": "roles", "description": "OpenID Connect scope for add user roles to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "true", "consent.screen.text": "${rolesScopeConsentText}"}, "protocolMappers": [{"id": "092402ae-89fa-4e06-974e-25434c4e2595", "name": "realm roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "realm_access.roles", "jsonType.label": "String", "multivalued": "true"}}, {"id": "b125807c-f6f9-41fc-8c6b-399907344946", "name": "client roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-client-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "resource_access.${client_id}.roles", "jsonType.label": "String", "multivalued": "true"}}, {"id": "2de31ffc-0650-4d97-b6d3-cc78b2e3e4bb", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}]}, {"id": "4f9dbc8d-6da1-48a3-a352-474352327ee3", "name": "internal_api_scope", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "7c58a7ef-35af-42e2-9e80-f61b3416737d", "name": "roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"multivalued": "true", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "roles", "jsonType.label": "String"}}]}, {"id": "9d97b9e8-45e9-46b4-bb7f-000ddf493dac", "name": "offline_access", "description": "OpenID Connect built-in scope: offline_access", "protocol": "openid-connect", "attributes": {"consent.screen.text": "${offlineAccessScopeConsentText}", "display.on.consent.screen": "true"}}, {"id": "5844fb29-10c0-47a3-8f87-196eccb1dae2", "name": "demo_client_scope", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "987b9d8b-daf3-4ec1-af05-b95d41e73648", "name": "roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"multivalued": "true", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "roles", "jsonType.label": "String"}}]}, {"id": "6eff05be-fb1e-45ab-b61c-52dc43e57919", "name": "web-origins", "description": "OpenID Connect scope for add allowed web origins to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false", "consent.screen.text": ""}, "protocolMappers": [{"id": "0f80a2fe-a498-438a-972f-e7d13e0824a0", "name": "allowed web origins", "protocol": "openid-connect", "protocolMapper": "oidc-allowed-origins-mapper", "consentRequired": false, "config": {}}]}], "defaultDefaultClientScopes": ["role_list", "profile", "email", "roles", "web-origins"], "defaultOptionalClientScopes": ["offline_access", "address", "phone", "microprofile-jwt"], "browserSecurityHeaders": {"contentSecurityPolicyReportOnly": "", "xContentTypeOptions": "nosniff", "xRobotsTag": "none", "xFrameOptions": "SAMEORIGIN", "contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "xXSSProtection": "1; mode=block", "strictTransportSecurity": "max-age=31536000; includeSubDomains"}, "smtpServer": {}, "eventsEnabled": false, "eventsListeners": ["jboss-logging"], "enabledEventTypes": [], "adminEventsEnabled": false, "adminEventsDetailsEnabled": false, "identityProviders": [], "identityProviderMappers": [], "components": {"org.keycloak.services.clientregistration.policy.ClientRegistrationPolicy": [{"id": "15062c30-81b1-45ae-9c1f-6aeba1ebf566", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "anonymous", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "aa08ebe7-389d-4b3a-b117-e4aa3406ee85", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "authenticated", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["saml-user-property-mapper", "oidc-address-mapper", "oidc-usermodel-property-mapper", "saml-role-list-mapper", "oidc-sha256-pairwise-sub-mapper", "oidc-full-name-mapper", "saml-user-attribute-mapper", "oidc-usermodel-attribute-mapper"]}}, {"id": "1dc239e4-2a02-4fbe-9244-85d80c1f1869", "name": "Trusted Hosts", "providerId": "trusted-hosts", "subType": "anonymous", "subComponents": {}, "config": {"host-sending-registration-request-must-match": ["true"], "client-uris-must-match": ["true"]}}, {"id": "3aaae71e-47a3-4984-951d-5059835d7f85", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "authenticated", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "310f5861-58d1-4da1-addb-f0400d136fe1", "name": "Full Scope Disabled", "providerId": "scope", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "39da5517-c0b7-47c9-bd47-73642ed35636", "name": "Consent Required", "providerId": "consent-required", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "312b60c8-8509-4517-bd59-7058fc949910", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "anonymous", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-usermodel-attribute-mapper", "oidc-full-name-mapper", "saml-user-property-mapper", "saml-user-attribute-mapper", "saml-role-list-mapper", "oidc-address-mapper", "oidc-usermodel-property-mapper", "oidc-sha256-pairwise-sub-mapper"]}}, {"id": "fdaeb93a-200d-4fb9-bfac-5c496a145564", "name": "Max Clients Limit", "providerId": "max-clients", "subType": "anonymous", "subComponents": {}, "config": {"max-clients": ["200"]}}], "org.keycloak.keys.KeyProvider": [{"id": "ceee678a-3b13-4bc3-81c0-d18beaf58063", "name": "rsa-generated", "providerId": "rsa-generated", "subComponents": {}, "config": {"privateKey": ["MIIEpQIBAAKCAQEAnwAdTUnx/nOSYjkAOFT1AUcoPS9rXTNqTpzVzUeBe4QIvXxFp2l0T2CPzbOe7Rj2t5jcgtqYPrAB/UOlhtXMJJulvlflBBgDcz6f/0YK0a2T94EHXw6iW8InzlitKIUPvLraOZWPivE1nqKxEcXfbCQfPClrZ78+487C1LGh09yo0rkfd2vh5Rc3ntO2WokPt44yh0zdyVSK5+AiVL9OzfjkBE2AjhO4x9yR+4cUkyXcYzThWqsqXoYyZgyFYrtTbCpgdNvQ+5HYU1xb6AVnPMzUigchcH7L+yYiYrQmiw8U7FxDus1vGPGF5gLVyEsfIslHBm1mqo+G3BnapwgNuQIDAQABAoIBAAtci7t5HDStd3lTT2O3FmbH+eUdXVOkl+UUKeboRCVaUvGPTr7etzPw13XAbwvMK90ErEUhb52TICFD0zEmV9cJ1Dccur2nJZ/3iqFIFP/Uom0eKDOvrQRXhFanWYA6udwnke5pdH9eKxGx2GtHs2Zb0muPHiCkts66eIuQQdsMnQIiYZKZZiQYfYl/1iKL6twBy08HFto2mV8Pc7IjsTOxr/LjN/GxMSajsl6Xcs4w3+9+xp80eJCYo+h0etzFOh0vbfMRAPA0Bl+MM+XTEPuG6DGXK9M/m9ALSRTtNr40zXS7k7Nsdm/iTD6mqJzHmnheHMR71D23rSMPh4/eI3ECgYEA+GVZMbY+kUXPaNRX4c1kJKml3KLK+WrGNmBArTioabsiY1aZeh+0zWlxeYh3Jz7G6zow7KYJow+9nsklJm3thFfRhDqTTOWBNwUbdCjzyrtbD0wxRGNnDRZAN9S4jyKULWrrXxd9+I1QpoDEcUv1GHcNrNrtfeuiIolkWG0uxU0CgYEAo94vBNMb5AdiZTtZIzHLdnTOvBKfowb05+1rl8pJxSCspe3fKmf3qg1pdH2tZccvwi6bba6c1wG/h3jNmyEwDLhQmS4mZ3wSyQQ0XMjNICnEOMciz2qaLMqZNpVKtf997e1Lel4YNBuF+FNtjgH7bl+bLZIvIM2VD1UF8VcAhB0CgYEAl6EWb5g2XWvmDrIA65/7j73Xt/XQPa0GTk83bAZ4GALaHRXSHpCYSAQj9vJs+hICT7l4AB3jKB0FvZZADVNxeCY7iaJQJNK8rB98AM6283ynO5FhScP8NirtDKfX+dg75/WqtKOcjEuHffyA7kBV35ddS7Vg4Esk8Dw6exg7QskCgYEAn9MkqN/+Y/mPzoyD8/+tImvq9yRy7t7qHwtB1VpHeSzVN0E6WHHu7L0IDcZGSO5tdkde8StNbacW11rLrnJAGyfp9H0+l9A7wjOHnrd62dPivj37hbHDjcxMgyA5Lily+YxoAYGkua3ejMVSJR9py80T3O6naKcaq0mR8kGZD0ECgYEA2lIzZnbrhWyifxG/0xzuPoDMj1kitNXdehZZU2N9mJW0B2EQOAhf+0DdTB/cYt297clvuSganyI4p1GclZ8zhFqqpLbvdwuDeK3GKN1yFg4pYQVfN06rAqaAxzUECS+tYfJaP03BTBiH5/XBPnpLeeJ0GrjZNrWE8xUjof2/ZDs="], "keyUse": ["SIG"], "certificate": ["MIICmzCCAYMCBgF/9UqDnjANBgkqhkiG9w0BAQsFADARMQ8wDQYDVQQDDAZhcGktZ3cwHhcNMjIwNDA0MTU1NDM1WhcNMzIwNDA0MTU1NjE1WjARMQ8wDQYDVQQDDAZhcGktZ3cwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCfAB1NSfH+c5JiOQA4VPUBRyg9L2tdM2pOnNXNR4F7hAi9fEWnaXRPYI/Ns57tGPa3mNyC2pg+sAH9Q6WG1cwkm6W+V+UEGANzPp//RgrRrZP3gQdfDqJbwifOWK0ohQ+8uto5lY+K8TWeorERxd9sJB88KWtnvz7jzsLUsaHT3KjSuR93a+HlFzee07ZaiQ+3jjKHTN3JVIrn4CJUv07N+OQETYCOE7jH3JH7hxSTJdxjNOFaqypehjJmDIViu1NsKmB029D7kdhTXFvoBWc8zNSKByFwfsv7JiJitCaLDxTsXEO6zW8Y8YXmAtXISx8iyUcGbWaqj4bcGdqnCA25AgMBAAEwDQYJKoZIhvcNAQELBQADggEBAFIifyUAWPp7PMQ/sgZkTWPCKXGW5Tf8qkFNJZ00nw3Uldv6ZCIHxzgjmuba7cGCHiykDKbRBzLSJ0AVMCN1MTOlsLl9DABMgCSD3p/BMp3Tuu7GMok1cqxJ/PBPF5mjJTS0+o82AUV3/aQxrsHmfaa0YwcT2OllJeBkhIXE7+nLeKfJIPJXNhbzg7GqI08rmWfiSbMAXA3y9QNYrohAXgqlbE38GG/ShBFpxBoBE8mB7oiKhh2AbfHEt3jsV9SgKc6ci4dSd6OFkQe033bis1cK1UZAhwKYl3D1cCQXBR8+V/9lNFZdP9f47yzQsiEz146djFA4FrJGJhha7O95UGM="], "priority": ["100"]}}, {"id": "746d42c9-3e31-41e3-b50a-e53565d3e82e", "name": "aes-generated", "providerId": "aes-generated", "subComponents": {}, "config": {"kid": ["c3c02213-695d-4947-b5eb-7b1ffc6a071e"], "secret": ["obO_dlhtJfo_xI-bmrFukQ"], "priority": ["100"]}}, {"id": "67eecfdb-53d8-4584-926f-765b5269f0f7", "name": "hmac-generated", "providerId": "hmac-generated", "subComponents": {}, "config": {"kid": ["c2a1d2f9-2c5f-4c99-b67d-48b1bc1d3341"], "secret": ["pvjXiQeEkmWVcls9GCW1G039Tn10E--J-_FqMAnmaVCYWg7zMCyyqv9Xu2bwhQoOn03V6SwUMjSBejVEFpUg9A"], "priority": ["100"], "algorithm": ["HS256"]}}, {"id": "752263dc-663d-4925-bafd-e3b73c31db78", "name": "rsa-enc-generated", "providerId": "rsa-enc-generated", "subComponents": {}, "config": {"privateKey": ["MIIEowIBAAKCAQEAoTC9bM60UBo1jDgQnMmH1fXN4MNA36VNsLtRdsZI803WCdqcr5P/23YJiWUBAUhXREUDXJu/V43USr0Az/Iz3yrljErLxpb3UYipIcHugLJP8psGx9hDF8oTyE8fVyxYQNetYj02oLqLbzmNdh8JjvfNDnqJ8JzBx+iSM2Y0d/OulidcgBb8asDxwf3oKCHMkD/o8epGkfF8feX5QMi5pWP3jRiGl4atn3mpEVXNCH8gD13otM/0XR1NurOah5nnJvgkL0uFj/xbFm+YxVNJdhuLoJ4vGVh1ryCdrMjhrK/AZhJpkojQNoNOPGAEVa6GlmjFZbnlwvhbScJl0Ur14wIDAQABAoIBAClooAEHyQKa+Q6giIP+scqhbtZC0rOhf6fbNz5QCgWXBVlas83XM29pj2GgdPWjY+fB4u8zg6A2a0cX7tF84x59oqK7xEWsGrTzap+ldmPRawBkGwXT5MiDvTlEOkTmu3VM8tm95/66bX+RRWoG5ym3NxJ1IGKToqR8drKFmw0FQHlZyKc27P9tnRRkagohbtRb863eCX/rw18GSKho6jgt+2YSwyN5GzT/ptnduudVf2vqRswQ9QwFEB06aavjatiqjmSwWGw3WAQ7+DwVR6w2/mlFSvJxcSnEyc4DdNydWVm1gguc2szEi3+uPV7F3b5HZ7YPn8xpD9n3+9yAmIECgYEA0EMpuOsGFjt/lU4BYUAIUV6Mr/jEbiMSZiOzHha+AWjWgsf6I8oOZT4M+9Qra2V5SCN9CTIcNr5NixbUIzFQL+Ma50aM5uXVWSfpJEu3QWo9/dZP9C2tIIho7cbsTXbHkhp8vNJKfbeM98coFjcLiqJZUaZFZiWPkivt+XGi1pECgYEAxiNkw0Fhm1ZyIfj7RggZlpj+lJq51cm4ak4hd0DjFaR+j6cKLLKkw4NhbUq4VA6r96YmiK6O/L0hMvGQqFMb0IFF7U34v663sxzfL+2DZveCpVMJvcC5FZqs7Zb0zaAhJCb8tgoosZjSU7erqJWtUOtn+pFsQl3WUneqnFrYRzMCgYB+8jGgckJHkwT18ZKNwQZQ8x02tqClseMpN2GbWamAGnOHTHaBEk0sEWLArle5IyWGf3tb+5uPEvkZa1TsDsceKYTgrPR+WmJt8hH++dg+m5AmBli+MolxZzTum2622UxVFqgqQNT/RL882bzpravvWX6wcnWjCgo1w2lPv6DOwQKBgE+EEsMdcQuDFMi3gDfptkMu4TRIJ/45Ny5KbrnKK5ga7MO/2KB5jQ/9oNLYosLxJGh9oIdH7pBVYoZsVY6H0+jIiISkvwhGGOjj1FoKqqpe+GpaudUSC/U0nuO2qkX/Vm2Iy/BOgWhMmVQJqPU+JFEOOCthwx2bRRDRf4HCH3vrAoGBAMJfuT7Q3D8cdOM8c6akk9Pabuf3QWn8Rz9hOkWuOl9EuKeOyK4SNPNLz4giiDnAXUe6lvCP6Poh/FOJBoNuWLmHQ5bDhYGwDBYuxWyzh5SVOr5O+9C0n6SiiduQJCCYentmQnWOWgXNUJcaHSAQVUsJwr/Dp3GJlFebA2vtX5Fj"], "keyUse": ["ENC"], "certificate": ["MIICmzCCAYMCBgF/9UqEUDANBgkqhkiG9w0BAQsFADARMQ8wDQYDVQQDDAZhcGktZ3cwHhcNMjIwNDA0MTU1NDM1WhcNMzIwNDA0MTU1NjE1WjARMQ8wDQYDVQQDDAZhcGktZ3cwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQChML1szrRQGjWMOBCcyYfV9c3gw0DfpU2wu1F2xkjzTdYJ2pyvk//bdgmJZQEBSFdERQNcm79XjdRKvQDP8jPfKuWMSsvGlvdRiKkhwe6Ask/ymwbH2EMXyhPITx9XLFhA161iPTaguotvOY12HwmO980OeonwnMHH6JIzZjR3866WJ1yAFvxqwPHB/egoIcyQP+jx6kaR8Xx95flAyLmlY/eNGIaXhq2feakRVc0IfyAPXei0z/RdHU26s5qHmecm+CQvS4WP/FsWb5jFU0l2G4ugni8ZWHWvIJ2syOGsr8BmEmmSiNA2g048YARVroaWaMVlueXC+FtJwmXRSvXjAgMBAAEwDQYJKoZIhvcNAQELBQADggEBAEg/K3zafsCgclYxiPapdCiBjcJ6+pJpqa9F8kY2eg/mj32N5cubL+qSJ5QgAlujqf5l+SqEiBlqUmprEU4W1BOOOk6xiF/XpyELbX4H5qSoEjNf3YL1F8yA8wFOaZ9kqtmI1PriyaEa9QU2zlYUCwyeZlVlrYveKiTNFjzfmRivRZoxffJzZCd2QcO5hilg+ft4G630919068hEZ6amdTkaLx8FhmMYcLjeOV6ykKoLdUwev7peyeFfrtSIuK6a8lDqlhW16WXsex0znH8utTWw45PupIu+F+M832rGDFKwdvFkOA/I43bWSgfI4R2OYPZqx0n/FakN6M9APySaaYE="], "priority": ["100"], "algorithm": ["RSA-OAEP"]}}]}, "internationalizationEnabled": false, "supportedLocales": [], "authenticationFlows": [{"id": "b1379043-99cd-4109-8896-8fbe5d2fa534", "alias": "Account verification options", "description": "Method with which to verity the existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-email-verification", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": true, "flowAlias": "Verify Existing Account by Re-authentication", "userSetupAllowed": false}]}, {"id": "b974c62a-e763-440c-ad93-a843df949a3d", "alias": "Authentication Options", "description": "Authentication options.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "basic-auth", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "basic-auth-otp", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-spnego", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "9e2ea161-f632-496a-98b7-8cdd22f69d15", "alias": "Browser - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-otp-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "3de09ee9-1919-4c6c-824f-201bfc505f53", "alias": "Direct Grant - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-otp", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "6d6383f8-313b-4893-9828-08288b4ee1b0", "alias": "First broker login - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-otp-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "ab66d6f5-509a-4939-b3b4-df6a057cd7e7", "alias": "<PERSON><PERSON> Existing Account", "description": "Handle what to do if there is existing account with same email/username like authenticated identity provider", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-confirm-link", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "Account verification options", "userSetupAllowed": false}]}, {"id": "5bb8ebbf-c374-481a-8d0c-affb9c18ba30", "alias": "Reset - Conditional OTP", "description": "Flow to determine if the OTP should be reset or not. Set to REQUIRED to force.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-otp", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "6f8c6952-30e2-4b5c-8d24-62f6f24d5d0b", "alias": "User creation or linking", "description": "Flow for the existing/non-existing user alternatives", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "create unique user config", "authenticator": "idp-create-user-if-unique", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": true, "flowAlias": "<PERSON><PERSON> Existing Account", "userSetupAllowed": false}]}, {"id": "2569f5b2-6593-4cbd-b8f0-a545efae471f", "alias": "Verify Existing Account by Re-authentication", "description": "Reauthentication of existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-username-password-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 20, "autheticatorFlow": true, "flowAlias": "First broker login - Conditional OTP", "userSetupAllowed": false}]}, {"id": "d8f1a9c6-04c3-4144-8f9f-35bac55170cf", "alias": "browser", "description": "browser based authentication", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-cookie", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-spnego", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "identity-provider-redirector", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 25, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 30, "autheticatorFlow": true, "flowAlias": "forms", "userSetupAllowed": false}]}, {"id": "943c8305-ac4c-4107-8ed7-954fbf7637c0", "alias": "clients", "description": "Base authentication for clients", "providerId": "client-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "client-secret", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-jwt", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-secret-jwt", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-x509", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 40, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "f9987624-fa9c-4909-ac5a-fb673269bd8a", "alias": "direct grant", "description": "OpenID Connect Resource Owner Grant", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "direct-grant-validate-username", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 30, "autheticatorFlow": true, "flowAlias": "Direct Grant - Conditional OTP", "userSetupAllowed": false}]}, {"id": "bfecd0b3-1ece-4cfb-bba5-cf5352beb5af", "alias": "docker auth", "description": "Used by Docker clients to authenticate against the IDP", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "docker-http-basic-authenticator", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "e6e94c87-67cc-4a77-9e29-12ab9c7f3e42", "alias": "first broker login", "description": "Actions taken after first broker login with identity provider account, which is not yet linked to any Keycloak account", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "review profile config", "authenticator": "idp-review-profile", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "User creation or linking", "userSetupAllowed": false}]}, {"id": "684ce0d9-d633-4ce2-bcb6-11cad92d902a", "alias": "forms", "description": "Username, password, otp and other auth forms.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-username-password-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 20, "autheticatorFlow": true, "flowAlias": "Browser - Conditional OTP", "userSetupAllowed": false}]}, {"id": "744a7609-acde-49a2-b351-37c36ff5e84f", "alias": "http challenge", "description": "An authentication flow based on challenge-response HTTP Authentication Schemes", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "no-cookie-redirect", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "Authentication Options", "userSetupAllowed": false}]}, {"id": "6d643cc9-e4da-4c85-944c-f95675c24f9e", "alias": "registration", "description": "registration flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-page-form", "authenticatorFlow": true, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": true, "flowAlias": "registration form", "userSetupAllowed": false}]}, {"id": "a0ecd573-e4b4-4299-9525-febec0f55500", "alias": "registration form", "description": "registration form", "providerId": "form-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-user-creation", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-profile-action", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 40, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-password-action", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 50, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-recaptcha-action", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 60, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "642377a5-94b6-4df3-8932-4eb067b4b783", "alias": "reset credentials", "description": "Reset credentials for a user if they forgot their password or something", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "reset-credentials-choose-user", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-credential-email", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 40, "autheticatorFlow": true, "flowAlias": "Reset - Conditional OTP", "userSetupAllowed": false}]}, {"id": "5de8b563-f9a7-430a-bcb7-379be4954654", "alias": "saml ecp", "description": "SAML ECP Profile Authentication Flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "http-basic-authenticator", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}]}], "authenticatorConfig": [{"id": "4743b422-2eba-4fb2-b496-ecbc30c9495c", "alias": "create unique user config", "config": {"require.password.update.after.registration": "false"}}, {"id": "99bda865-9f43-47ba-b2a4-6848616932a8", "alias": "review profile config", "config": {"update.profile.on.first.login": "missing"}}], "requiredActions": [{"alias": "CONFIGURE_TOTP", "name": "Configure OTP", "providerId": "CONFIGURE_TOTP", "enabled": true, "defaultAction": false, "priority": 10, "config": {}}, {"alias": "terms_and_conditions", "name": "Terms and Conditions", "providerId": "terms_and_conditions", "enabled": false, "defaultAction": false, "priority": 20, "config": {}}, {"alias": "UPDATE_PASSWORD", "name": "Update Password", "providerId": "UPDATE_PASSWORD", "enabled": true, "defaultAction": false, "priority": 30, "config": {}}, {"alias": "UPDATE_PROFILE", "name": "Update Profile", "providerId": "UPDATE_PROFILE", "enabled": true, "defaultAction": false, "priority": 40, "config": {}}, {"alias": "VERIFY_EMAIL", "name": "<PERSON><PERSON><PERSON>", "providerId": "VERIFY_EMAIL", "enabled": true, "defaultAction": false, "priority": 50, "config": {}}, {"alias": "delete_account", "name": "Delete Account", "providerId": "delete_account", "enabled": false, "defaultAction": false, "priority": 60, "config": {}}, {"alias": "update_user_locale", "name": "Update User Locale", "providerId": "update_user_locale", "enabled": true, "defaultAction": false, "priority": 1000, "config": {}}], "browserFlow": "browser", "registrationFlow": "registration", "directGrantFlow": "direct grant", "resetCredentialsFlow": "reset credentials", "clientAuthenticationFlow": "clients", "dockerAuthenticationFlow": "docker auth", "attributes": {"cibaBackchannelTokenDeliveryMode": "poll", "cibaExpiresIn": "120", "cibaAuthRequestedUserHint": "login_hint", "oauth2DeviceCodeLifespan": "600", "oauth2DevicePollingInterval": "5", "parRequestUriLifespan": "60", "cibaInterval": "5"}, "keycloakVersion": "17.0.1", "userManagedAccessAllowed": false, "clientProfiles": {"profiles": []}, "clientPolicies": {"policies": []}}
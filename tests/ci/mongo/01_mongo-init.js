// Main database setup
const apiDbName = _getEnv('MONGO_API_DATABASE');
const apiDb = db.getSiblingDB(apiDbName);

apiDb.createUser({
    user: _getEnv('MONGO_API_USER_NAME'),
    pwd: _getEnv('MONGO_API_USER_PASSWORD'),
    roles: [
        { role: 'readWrite', db: apiDbName }
    ]
});

apiDb.createCollection('jobs');

apiDb.createCollection('jobs');
apiDb.createCollection('services');
apiDb.jobs.createIndex({ 'job_id': 1 }, { unique: true });
apiDb.services.createIndex({ 'service_id': 1 }, { unique: true });

// GridFs database setup
const gridFsDbName = _getEnv('MONGO_API_GRIDFS_DATABASE');
const gridFsDb = db.getSiblingDB(gridFsDbName);

gridFsDb.createUser({
    user: _getEnv('MONGO_API_USER_NAME'),
    pwd: _getEnv('MONGO_API_USER_PASSWORD'),
    roles: [
        { role: 'readWrite', db: gridFsDbName },
    ]
});

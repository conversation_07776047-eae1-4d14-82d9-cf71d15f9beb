[tool:pytest]
# Pytest configuration for provider abstraction tests
testpaths = tests
python_files = test_providers.py test_router_providers.py
python_classes = Test*
python_functions = test_*
pythonpath = .
addopts =
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --asyncio-mode=auto
markers =
    unit: Unit tests for provider abstraction
    integration: Integration tests for provider workflows
    router: Router integration tests
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning

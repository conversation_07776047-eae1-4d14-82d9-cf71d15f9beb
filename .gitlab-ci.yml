include:
  - project: 'jn-api/templates'
    ref: 2.1.5
    file: 'changelog.yml'
  - project: 'jn-api/templates'
    ref: 2.1.5
    file: 'code_checker/python.yml'
  - project: 'jn-api/templates'
    ref: 2.1.11
    file: 'swarm_deploy.yml'
    inputs:
      swarm_stack_poc_file: "./swarm_stacks/common.yml"
      swarm_stack_pre_prod_file: "./swarm_stacks/pre-preproduction.yml"
      swarm_stack_prod_file: "./swarm_stacks/production.yml"
      image_poc_tag: "poc-${CI_COMMIT_SHORT_SHA}"
      image_pre_prod_tag: "pp-${CI_COMMIT_TAG}"
      stack_name: jso
  # - project: 'jn-api/templates'
  #   ref: main
  #   file: 'integration_tests/jnapi.yml'
  #   inputs:
  #     JNAPI_CLIENT_ID: "internal_api"
  #     JNAPI_CLIENT_SECRET: "p8Om4VEioeq5xTRNRI2DZTtTxTKUB6Kv"

stages:
  - code_checker
#  - integration-test
  - release
  - build
  - lint-docker-stack
  - stack-deploy

# integration_testing:
#   stage: integration-test
#   environment:
#     name: test
#   image: ghcr.io/astral-sh/uv:debian
  
    
#   # Add 'deployment' tag, in  order to reach Sentry server 
#   tags:
#     - deployment  
#   before_script:
#     - apt update && apt install python3-pip -y
#     - pip3 install just-bin --break-system-packages
#     - uv sync
#   services:
#     - !reference [ .jnapi_services, postgres ]
#     - !reference [ .jnapi_services, auth ]        
#     - !reference [ .jnapi_services, mongo ]
#     - !reference [ .jnapi_services, redis ]
#     # - !reference [ .jnapi_services, jn-api ]
#     # temporary workaround
#     - name: gitlab.as30781.net:4567/jn-api/infra/gateway-api-config/api:poc-latest
#       alias: gateway_api
#       variables:
#         KRAKEND_PORT: 8081
#         TZ: "Europe/Paris"
#         FC_ENABLE: 1
#         FC_PARTIALS: "/etc/krakend/includes"
#         FC_TEMPLATES: "/etc/krakend/templates"
#     - name: gitlab.as30781.net:4567/jn-api/infra/data-api/data-api:poc-latest
#       alias: data-api
#       entrypoint: 
#         - '/bin/bash'
#         - '-c'
#         - |
#           echo "Waiting for keycloak to start"
#           sleep 30
#           uvicorn app.main:app --host 0.0.0.0 --port 80 --root-path /data
#     - name: gitlab.as30781.net:4567/jn-api/infra/jobs-api/jobs-api:poc-latest
#       alias: jobs-api
#       entrypoint:       
#         - '/bin/bash'
#         - '-c'
#         - |
#           echo "Waiting for keycloak to start"
#           sleep 30
#           uvicorn app.main:app --host 0.0.0.0 --port 80 --root-path /jobs
#   script:
#     # Wait for all services to be up and running
#     - sleep 30
#     - source tests/set_env.sh
#     - just start_server &
#     - just start_runners &
#     - just tests
#   extends:
#     - .jnapi_variables
#     - .redis_variables
#     - .mongo_db_variables
#   variables:
#     CI_DEBUG_SERVICES: "true"
#     # CI_DEBUG_TRACE: "true"
#     # services must share a network (keycloak + postgres + kraken)
#     FF_NETWORK_PER_BUILD: 1
#     # init files to set up keycloak and mongo instances ; see project bootstrap-dev-environment
#     KEYCLOAK_INIT_FILE: "${CI_PROJECT_DIR}/tests/ci/keycloak/init_realm_api-gw.json"
#     MONGO_API_INIT_FILE: "${CI_PROJECT_DIR}/tests/ci/mongo/01_mongo-init.js"
#     INTEGRATION_TESTS: 1
#   except:
#     - tags

.build_deploy: &build_definition
  image:
    name: gitlab.as30781.net:4567/jn-api/infra/service-deployer/service-deployer:latest
  services:
    - docker:dind
  before_script:
    - export DOCKER_TLS_VERIFY=
    - cp $CONFIG_FILE .config
  script:
    - export DOCKER_BUILD_ARGS="--build-arg UV_VERSION=$UV_VERSION --build-arg UV_INDEX_DEFAULT_USERNAME=$UV_INDEX_DEFAULT_USERNAME --build-arg UV_INDEX_DEFAULT_PASSWORD=$UV_INDEX_DEFAULT_PASSWORD --build-arg SENTRY_DSN=$SENTRY_DSN --build-arg SENTRY_PROJECT=$SENTRY_PROJECT  --build-arg ENVIRONMENT=$ENVIRONMENT --build-arg VERSION=$VERSION "
    - /build.sh
    - /sentry-release.sh
  tags:
    - deployment

poc:
  <<: *build_definition
  stage: build
  variables:
    ENVIRONMENT: "poc"
    VERSION: "dev"
    DOCKER_DRIVER: overlay2
    DOCKER_TLS_CERTDIR: ""
  environment:
    name: poc
  except:
      - tags

pre-production:
  <<: *build_definition
  stage: build
  variables:
    ENVIRONMENT: "pre-production"
    VERSION: $CI_COMMIT_TAG
    DOCKER_DRIVER: overlay2
    DOCKER_TLS_CERTDIR: ""
  environment:
    name: pre-production
  except:
    - branches
  only:
    - tags
  when: manual

production:
  <<: *build_definition
  stage: build
  variables:
    ENVIRONMENT: "production"
    VERSION: $CI_COMMIT_TAG
    DOCKER_DRIVER: overlay2
    DOCKER_TLS_CERTDIR: ""
  environment:
    name: production
  except:
    - branches
  only:
    - tags
  needs:
    - pre-production    
  when: manual

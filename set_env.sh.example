export GATEWAY_API_URL='http://localhost:8081'

export IDP_ROOT_URI='{"corp": "https://sso.freepro.com/realms/Corp/"}'
export IDENTITY_PROVIDER_URL='https://sso.freepro.com'
export IDENTITY_PROVIDER_REALM=Corp
export IDENTITY_PROVIDER_TOKEN_URL=${IDENTITY_PROVIDER_URL}/realms/${IDENTITY_PROVIDER_REALM}/protocol/openid-connect/token

export CLIENT_ID=m2m-apigw-internal
export CLIENT_SECRET=XXXX

export CLIENT_ID_JSO=m2m-apigw-oss-admin
export CLIENT_SECRET_JSO=XXXX

export UV_VERSION=python3.10-alpine
export UV_INDEX_DEFAULT_USERNAME=user-ro-access
export UV_INDEX_DEFAULT_PASSWORD=XXXX

# Variables required for metrics tests
export TEST_REGION=fr-lyo
export TEST_ACCOUNT_ID=**********
export TEST_USER=foo
export TEST_BUCKET=bar
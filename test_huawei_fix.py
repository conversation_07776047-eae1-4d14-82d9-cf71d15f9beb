#!/usr/bin/env python3
"""
Quick test to verify the Huawei OBSClient fix.
"""

import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def test_obs_client_instantiation():
    """Test that OBSClient can be instantiated without async issues."""
    try:
        from api.providers.huawei.obs_client import OBSClient
        
        # Test instantiation with dummy values
        client = OBSClient(
            host="localhost",
            port="8080",
            use_https=False,
            username="test",
            password="test",
            esn="test-esn"
        )
        
        print("✅ OBSClient instantiated successfully")
        print(f"   Host: {client.host}")
        print(f"   Port: {client.port}")
        print(f"   Base URL: {client.base_url}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to instantiate OBSClient: {e}")
        return False

def test_huawei_metrics_provider_instantiation():
    """Test that HuaweiMetricsProvider can be instantiated."""
    try:
        from api.providers.huawei_provider import HuaweiMetricsProvider
        
        # Mock config
        config = {
            "poe_access_key": "test",
            "poe_secret_key": "test", 
            "poe_endpoint": "localhost",
            "poe_port": "8080",
            "obs_host": "localhost",
            "obs_port": "8080",
            "obs_username": "test",
            "obs_password": "test",
            "esn": "test-esn"
        }
        
        provider = HuaweiMetricsProvider("test-region", config)
        print("✅ HuaweiMetricsProvider instantiated successfully")
        print(f"   Region: {provider.region}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to instantiate HuaweiMetricsProvider: {e}")
        return False

if __name__ == "__main__":
    print("Testing Huawei OBSClient fix...")
    
    success1 = test_obs_client_instantiation()
    success2 = test_huawei_metrics_provider_instantiation()
    
    if success1 and success2:
        print("\n🎉 All tests passed! The async __init__ fix is working.")
        sys.exit(0)
    else:
        print("\n💥 Some tests failed.")
        sys.exit(1)

# This file controls the behavior of Trunk: https://docs.trunk.io/cli
# To learn more about the format of this file, see https://docs.trunk.io/reference/trunk-yaml
version: 0.1
cli:
  version: 1.22.11
# Trunk provides extensibility via plugins. (https://docs.trunk.io/plugins)
plugins:
  sources:
    - id: trunk
      ref: v1.6.7
      uri: https://github.com/trunk-io/plugins
# Many linters and tools depend on runtimes - configure them here. (https://docs.trunk.io/runtimes)
runtimes:
  enabled:
    - go@1.21.0
    - node@18.20.5
    - python@3.10.8
# This is the section where you manage your linters. (https://docs.trunk.io/check/configuration)
lint:
  enabled:
    - bandit@1.8.3
    - black@25.1.0
    - checkov@3.2.390
    - dotenv-linter@3.3.0
    - git-diff-check
    - hadolint@2.12.1-beta
    - isort@6.0.1
    - markdownlint@0.44.0
    - prettier@3.5.3
    - ruff@0.11.1
    - shellcheck@0.10.0
    - shfmt@3.6.0
    - taplo@0.9.3
    - trufflehog@3.88.18
    - yamllint@1.36.2
actions:
  disabled:
    - trunk-announce
    - trunk-check-pre-push
    - trunk-fmt-pre-commit
  enabled:
    - trunk-upgrade-available
